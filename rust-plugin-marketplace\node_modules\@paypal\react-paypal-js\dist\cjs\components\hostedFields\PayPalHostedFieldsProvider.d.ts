import type { FC } from "react";
import type { PayPalHostedFieldsComponentProps } from "../../types/payPalHostedFieldTypes";
/**
This `<PayPalHostedFieldsProvider />` provider component wraps the form field elements and accepts props like `createOrder()`.

This provider component is designed to be used with the `<PayPalHostedField />` component.

Warning: If you don't see anything in the screen probably your client is ineligible.
To handle this problem make sure to use the prop `notEligibleError` and pass a component with a custom message.
Take a look to this link if that is the case: https://developer.paypal.com/docs/checkout/advanced/integrate/
*/
export declare const PayPalHostedFieldsProvider: FC<PayPalHostedFieldsComponentProps>;
