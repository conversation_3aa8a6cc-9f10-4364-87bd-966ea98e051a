/*!
 * react-paypal-js v8.8.3 (2025-04-11T19:50:46.506Z)
 * Copyright 2020-present, PayPal, Inc. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import e,{createContext as t,useContext as n,useRef as r,useState as o,useEffect as a,useReducer as i}from"react";var c,l,u;!function(e){e.INITIAL="initial",e.PENDING="pending",e.REJECTED="rejected",e.RESOLVED="resolved"}(c||(c={})),function(e){e.LOADING_STATUS="setLoadingStatus",e.RESET_OPTIONS="resetOptions",e.SET_BRAINTREE_INSTANCE="braintreeInstance"}(l||(l={})),function(e){e.NUMBER="number",e.CVV="cvv",e.EXPIRATION_DATE="expirationDate",e.EXPIRATION_MONTH="expirationMonth",e.EXPIRATION_YEAR="expirationYear",e.POSTAL_CODE="postalCode"}(u||(u={}));var s=function(){return s=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},s.apply(this,arguments)};function d(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}function f(e,t,n){if(n||2===arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}"function"==typeof SuppressedError&&SuppressedError;var p="data-react-paypal-script-id",v="dataClientToken",m="dataJsSdkLibrary",h="react-paypal-js",y="dataNamespace",E="dataSdkIntegrationSource",P="dataUserIdToken",b="Failed to load the PayPal JS SDK script.",N="3.117.0",g="https://js.braintreegateway.com/web/".concat(N,"/js/client.min.js"),w="https://js.braintreegateway.com/web/".concat(N,"/js/paypal-checkout.min.js"),I="paypal",S="usePayPalScriptReducer must be used within a PayPalScriptProvider";function A(e){return void 0===e&&(e=I),window[e]}function O(e){var t=e.reactComponentName,n=e.sdkComponentKey,r=e.sdkRequestedComponents,o=void 0===r?"":r,a=e.sdkDataNamespace,i=void 0===a?I:a,c=n.charAt(0).toUpperCase().concat(n.substring(1)),l="Unable to render <".concat(t," /> because window.").concat(i,".").concat(c," is undefined."),u="string"==typeof o?o:o.join(",");if(!u.includes(n)){var s=[u,n].filter(Boolean).join();l+="\nTo fix the issue, add '".concat(n,"' to the list of components passed to the parent PayPalScriptProvider:")+"\n`<PayPalScriptProvider options={{ components: '".concat(s,"'}}>`.")}return l}function T(e){var t=e,n=p;t[n];var r=d(t,[n+""]);return"react-paypal-js-".concat(function(e){for(var t="",n=0;n<e.length;n++){var r=e[n].charCodeAt(0)*n;e[n+1]&&(r+=e[n+1].charCodeAt(0)*(n-1)),t+=String.fromCharCode(97+Math.abs(r)%26)}return t}(JSON.stringify(r)))}function C(e){var t=self.document.querySelector("script[".concat(p,'="').concat(e,'"]'));(null==t?void 0:t.parentNode)&&t.parentNode.removeChild(t)}function R(e,t){var n,r;switch(t.type){case l.LOADING_STATUS:return"object"==typeof t.value?s(s({},e),{loadingStatus:t.value.state,loadingStatusErrorMessage:t.value.message}):s(s({},e),{loadingStatus:t.value});case l.RESET_OPTIONS:return C(e.options[p]),s(s({},e),{loadingStatus:c.PENDING,options:s(s((n={},n[E]=h,n),t.value),(r={},r[p]="".concat(T(t.value)),r))});case l.SET_BRAINTREE_INSTANCE:return s(s({},e),{braintreePayPalCheckoutInstance:t.value});default:return e}}var F=t(null);function k(e){if("function"==typeof(null==e?void 0:e.dispatch)&&0!==e.dispatch.length)return e;throw new Error(S)}function j(){var e=k(n(F));return[s(s({},e),{isInitial:e.loadingStatus===c.INITIAL,isPending:e.loadingStatus===c.PENDING,isResolved:e.loadingStatus===c.RESOLVED,isRejected:e.loadingStatus===c.REJECTED}),e.dispatch]}function D(){var e=function(e){var t,n;if(!(null===(t=null==e?void 0:e.options)||void 0===t?void 0:t[v])&&!(null===(n=null==e?void 0:e.options)||void 0===n?void 0:n[P]))throw new Error("Invalid authorization data. Use dataClientToken or dataUserIdToken to authorize.");return e}(k(n(F)));return[e,e.dispatch]}var x=t({});function L(){return n(x)}var B=function(t){var n,i,c,l=t.className,u=void 0===l?"":l,p=t.disabled,v=void 0!==p&&p,m=t.children,h=t.forceReRender,E=void 0===h?[]:h,P=d(t,["className","disabled","children","forceReRender"]),b=v?{opacity:.38}:{},N="".concat(u," ").concat(v?"paypal-buttons-disabled":"").trim(),g=r(null),w=r(null),I=(i=P,c=r(new Proxy({},{get:function(e,t,n){return"function"==typeof e[t]?function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return e[t].apply(e,n)}:Reflect.get(e,t,n)}})),c.current=Object.assign(c.current,i),c.current),S=j()[0],T=S.isResolved,C=S.options,R=o(null),F=R[0],k=R[1],D=o(!0),x=D[0],L=D[1],M=o(null)[1];function U(){null!==w.current&&w.current.close().catch((function(){}))}return(null===(n=w.current)||void 0===n?void 0:n.updateProps)&&w.current.updateProps({message:P.message}),a((function(){if(!1===T)return U;var e=A(C.dataNamespace);if(void 0===e||void 0===e.Buttons)return M((function(){throw new Error(O({reactComponentName:B.displayName,sdkComponentKey:"buttons",sdkRequestedComponents:C.components,sdkDataNamespace:C[y]}))})),U;try{w.current=e.Buttons(s(s({},I),{onInit:function(e,t){k(t),"function"==typeof P.onInit&&P.onInit(e,t)}}))}catch(e){return M((function(){throw new Error("Failed to render <PayPalButtons /> component. Failed to initialize:  ".concat(e))}))}return!1===w.current.isEligible()?(L(!1),U):g.current?(w.current.render(g.current).catch((function(e){null!==g.current&&0!==g.current.children.length&&M((function(){throw new Error("Failed to render <PayPalButtons /> component. ".concat(e))}))})),U):U}),f(f([T],E,!0),[P.fundingSource],!1)),a((function(){null!==F&&(!0===v?F.disable().catch((function(){})):F.enable().catch((function(){})))}),[v,F]),e.createElement(e.Fragment,null,x?e.createElement("div",{ref:g,style:b,className:N}):m)};function M(e){var t=e.sdkBaseUrl,n=e.environment,r=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(e,["sdkBaseUrl","environment"]),o=t||function(e){return"sandbox"===e?"https://www.sandbox.paypal.com/sdk/js":"https://www.paypal.com/sdk/js"}(n),a=r,i=Object.keys(a).filter((function(e){return void 0!==a[e]&&null!==a[e]&&""!==a[e]})).reduce((function(e,t){var n,r=a[t].toString();return n=function(e,t){return(t?"-":"")+e.toLowerCase()},"data"===(t=t.replace(/[A-Z]+(?![a-z])|[A-Z]/g,n)).substring(0,4)||"crossorigin"===t?e.attributes[t]=r:e.queryParams[t]=r,e}),{queryParams:{},attributes:{}}),c=i.queryParams,l=i.attributes;return c["merchant-id"]&&-1!==c["merchant-id"].indexOf(",")&&(l["data-merchant-id"]=c["merchant-id"],c["merchant-id"]="*"),{url:"".concat(o,"?").concat(U(c)),attributes:l}}function U(e){var t="";return Object.keys(e).forEach((function(n){0!==t.length&&(t+="&"),t+=n+"="+e[n]})),t}function _(e,t){void 0===t&&(t={});var n=document.createElement("script");return n.src=e,Object.keys(t).forEach((function(e){n.setAttribute(e,t[e]),"data-csp-nonce"===e&&n.setAttribute("nonce",t["data-csp-nonce"])})),n}function H(e,t){if(void 0===t&&(t=Promise),Y(e,t),"undefined"==typeof document)return t.resolve(null);var n=M(e),r=n.url,o=n.attributes,a=o["data-namespace"]||"paypal",i=V(a);return o["data-js-sdk-library"]||(o["data-js-sdk-library"]="paypal-js"),function(e,t){var n=document.querySelector('script[src="'.concat(e,'"]'));if(null===n)return null;var r=_(e,t),o=n.cloneNode();if(delete o.dataset.uidAuto,Object.keys(o.dataset).length!==Object.keys(r.dataset).length)return null;var a=!0;return Object.keys(o.dataset).forEach((function(e){o.dataset[e]!==r.dataset[e]&&(a=!1)})),a?n:null}(r,o)&&i?t.resolve(i):G({url:r,attributes:o},t).then((function(){var e=V(a);if(e)return e;throw new Error("The window.".concat(a," global variable is not available."))}))}function G(e,t){void 0===t&&(t=Promise),Y(e,t);var n=e.url,r=e.attributes;if("string"!=typeof n||0===n.length)throw new Error("Invalid url.");if(void 0!==r&&"object"!=typeof r)throw new Error("Expected attributes to be an object.");return new t((function(e,t){if("undefined"==typeof document)return e();!function(e){var t=e.url,n=e.attributes,r=e.onSuccess,o=e.onError,a=_(t,n);a.onerror=o,a.onload=r,document.head.insertBefore(a,document.head.firstElementChild)}({url:n,attributes:r,onSuccess:function(){return e()},onError:function(){var e=new Error('The script "'.concat(n,'" failed to load. Check the HTTP status code and response body in DevTools to learn more.'));return t(e)}})}))}function V(e){return window[e]}function Y(e,t){if("object"!=typeof e||null===e)throw new Error("Expected an options object.");var n=e.environment;if(n&&"production"!==n&&"sandbox"!==n)throw new Error('The `environment` option must be either "production" or "sandbox".');if(void 0!==t&&"function"!=typeof t)throw new Error("Expected PromisePonyfill to be a function.")}B.displayName="PayPalButtons","function"==typeof SuppressedError&&SuppressedError;var q=function(e){return e&&function(e){var t,n;if("function"!=typeof(null===(t=null==e?void 0:e.client)||void 0===t?void 0:t.create)&&"function"!=typeof(null===(n=null==e?void 0:e.paypalCheckout)||void 0===n?void 0:n.create))throw new Error("The braintreeNamespace property is not a valid BraintreeNamespace type.");return!0}(e)?Promise.resolve(e):Promise.all([G({url:g}),G({url:w})]).then((function(){return void 0===e&&(e="braintree"),window[e];var e}))},X=function(t){var n=t.className,r=void 0===n?"":n,i=t.disabled,c=void 0!==i&&i,u=t.children,f=t.forceReRender,p=void 0===f?[]:f,m=t.braintreeNamespace,h=t.merchantAccountId,y=d(t,["className","disabled","children","forceReRender","braintreeNamespace","merchantAccountId"]),E=o(null)[1],N=D(),g=N[0],w=N[1];return a((function(){q(m).then((function(e){var t=g.options[P],n=g.options[v];return e.client.create({authorization:t||n}).then((function(t){var n=h?{merchantAccountId:h}:{};return e.paypalCheckout.create(s(s({},n),{client:t}))})).then((function(e){w({type:l.SET_BRAINTREE_INSTANCE,value:e})}))})).catch((function(e){E((function(){throw new Error("".concat(b," ").concat(e))}))}))}),[g.options]),e.createElement(e.Fragment,null,g.braintreePayPalCheckoutInstance&&e.createElement(B,s({className:r,disabled:c,forceReRender:p},function(e,t){var n=e.createOrder,r=e.createBillingAgreement,o=e.onApprove;return"function"==typeof n&&(e.createOrder=function(e,r){return n(e,s(s({},r),{braintree:t}))}),"function"==typeof r&&(e.createBillingAgreement=function(e,n){return r(e,s(s({},n),{braintree:t}))}),"function"==typeof o&&(e.onApprove=function(e,n){return o(e,s(s({},n),{braintree:t}))}),s({},e)}(y,g.braintreePayPalCheckoutInstance)),u))},z=function(t){var n=t.className,i=void 0===n?"":n,c=t.children,l=d(t,["className","children"]),u=j()[0],f=u.isResolved,p=u.options,v=r(null),m=o(!0),h=m[0],E=m[1],P=o(null)[1];return a((function(){if(!1!==f){var e=A(p[y]);if(void 0===e||void 0===e.Marks)return P((function(){throw new Error(O({reactComponentName:z.displayName,sdkComponentKey:"marks",sdkRequestedComponents:p.components,sdkDataNamespace:p[y]}))}));!function(e){var t=v.current;if(!t||!e.isEligible())return E(!1);t.firstChild&&t.removeChild(t.firstChild),e.render(t).catch((function(e){null!==t&&0!==t.children.length&&P((function(){throw new Error("Failed to render <PayPalMarks /> component. ".concat(e))}))}))}(e.Marks(s({},l)))}}),[f,l.fundingSource]),e.createElement(e.Fragment,null,h?e.createElement("div",{ref:v,className:i}):c)};z.displayName="PayPalMarks";var K=function(t){var n=t.className,i=void 0===n?"":n,c=t.forceReRender,l=void 0===c?[]:c,u=d(t,["className","forceReRender"]),p=j()[0],v=p.isResolved,m=p.options,h=r(null),E=r(null),P=o(null)[1];return a((function(){if(!1!==v){var e=A(m[y]);if(void 0===e||void 0===e.Messages)return P((function(){throw new Error(O({reactComponentName:K.displayName,sdkComponentKey:"messages",sdkRequestedComponents:m.components,sdkDataNamespace:m[y]}))}));E.current=e.Messages(s({},u)),E.current.render(h.current).catch((function(e){null!==h.current&&0!==h.current.children.length&&P((function(){throw new Error("Failed to render <PayPalMessages /> component. ".concat(e))}))}))}}),f([v],l,!0)),e.createElement("div",{ref:h,className:i})};K.displayName="PayPalMessages";var J=function(t){var n,r=t.options,o=void 0===r?{clientId:"test"}:r,u=t.children,d=t.deferLoading,f=void 0!==d&&d,v=i(R,{options:s(s({},o),(n={},n[m]=h,n[E]=h,n[p]="".concat(T(o)),n)),loadingStatus:f?c.INITIAL:c.PENDING}),y=v[0],P=v[1];return a((function(){if(!1===f&&y.loadingStatus===c.INITIAL)return P({type:l.LOADING_STATUS,value:c.PENDING});if(y.loadingStatus===c.PENDING){var e=!0;return H(y.options).then((function(){e&&P({type:l.LOADING_STATUS,value:c.RESOLVED})})).catch((function(t){console.error("".concat(b," ").concat(t)),e&&P({type:l.LOADING_STATUS,value:{state:c.REJECTED,message:String(t)}})})),function(){e=!1}}}),[y.options,f,y.loadingStatus]),e.createElement(F.Provider,{value:s(s({},y),{dispatch:P})},u)},Z=function(e){if(!e.includes(u.NUMBER)||!e.includes(u.CVV)||function(e){return!e.includes(u.EXPIRATION_DATE)&&!e.includes(u.EXPIRATION_MONTH)&&!e.includes(u.EXPIRATION_YEAR)}(e))throw new Error("To use HostedFields you must use it with at least 3 children with types: [number, cvv, expirationDate] includes")},W=function(e){Z(e),function(e){if(e.length!==new Set(e).size)throw new Error("Cannot use duplicate HostedFields as children")}(e)},Q=function(t){var n=t.styles,i=t.createOrder,l=t.notEligibleError,u=t.children,d=t.installments,f=D()[0],p=f.options,v=f.loadingStatus,m=o(!0),h=m[0],E=m[1],P=o(),b=P[0],N=P[1],g=o(null)[1],w=r(null),S=r(),O=function(e){void 0===e&&(e={});var t=r(e);return[t,function(e){t.current=s(s({},t.current),e)}]}(),T=O[0],C=O[1];return a((function(){var e;if(W(Object.keys(T.current)),v===c.RESOLVED){if(S.current=A(p[y]).HostedFields,!S.current)throw new Error(function(e){var t=e.components,n=void 0===t?"":t,r=e[y],o=void 0===r?I:r,a=n?"".concat(n,",hosted-fields"):"hosted-fields",i="Unable to render <PayPalHostedFieldsProvider /> because window.".concat(o,".HostedFields is undefined.");return n.includes("hosted-fields")||(i+="\nTo fix the issue, add 'hosted-fields' to the list of components passed to the parent PayPalScriptProvider: <PayPalScriptProvider options={{ components: '".concat(a,"'}}>")),i}(((e={components:p.components})[y]=p[y],e)));if(!S.current.isEligible())return E(!1);b&&b.teardown(),S.current.render({createOrder:i,fields:T.current,installments:d,styles:n}).then((function(e){w.current&&N(e)})).catch((function(e){g((function(){throw new Error("Failed to render <PayPalHostedFieldsProvider /> component. ".concat(e))}))}))}}),[v,n]),e.createElement("div",{ref:w},h?e.createElement(x.Provider,{value:{cardFields:b,registerHostedField:C}},u):l)},$=function(t){var r=t.hostedFieldType,o=t.options,i=d(t,["hostedFieldType","options"]),c=n(x);return a((function(){var e;if(!(null==c?void 0:c.registerHostedField))throw new Error("The HostedField cannot be register in the PayPalHostedFieldsProvider parent component");c.registerHostedField(((e={})[r]={selector:o.selector,placeholder:o.placeholder,type:o.type,formatInput:o.formatInput,maskInput:o.maskInput,select:o.select,maxlength:o.maxlength,minlength:o.minlength,prefill:o.prefill,rejectUnsupportedCards:o.rejectUnsupportedCards},e))}),[]),e.createElement("div",s({},i))};function ee(){}var te=t({cardFieldsForm:null,fields:{},registerField:ee,unregisterField:ee}),ne=function(){return n(te)},re=function(t){var n=t.children;return e.createElement("div",{style:{width:"100%"}},n)},oe=function(t){var n=t.children,i=d(t,["children"]),c=j()[0],l=c.isResolved,u=c.options,f=function(){var e=o(null)[1],t=r({});return{fields:t.current,registerField:function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var o=n[0],a=n[1],i=n[2];return t.current[o]&&e((function(){throw new Error("Cannot use duplicate CardFields as children")})),t.current[o]=null==i?void 0:i[o](a),t.current[o]},unregisterField:function(e){var n=t.current[e];n&&(n.close().catch(ee),delete t.current[e])}}}(),p=f.fields,v=f.registerField,m=f.unregisterField,h=o(null),E=h[0],P=h[1],b=r(null),N=o(!1),g=N[0],w=N[1],S=o(null)[1];return a((function(){var e,t,n;if(l){try{b.current=null!==(n=null===(t=(e=A(u[y])).CardFields)||void 0===t?void 0:t.call(e,s({},i)))&&void 0!==n?n:null}catch(e){return void S((function(){throw new Error("Failed to render <PayPalCardFieldsProvider /> component. Failed to initialize:  ".concat(e))}))}if(b.current)return w(b.current.isEligible()),P(b.current),function(){P(null),b.current=null};S((function(){var e;throw new Error(function(e){var t=e.components,n=void 0===t?"":t,r=e[y],o=void 0===r?I:r,a=n?"".concat(n,",card-fields"):"card-fields",i="Unable to render <PayPalCardFieldsProvider /> because window.".concat(o,".CardFields is undefined.");return n.includes("card-fields")||(i+="\nTo fix the issue, add 'card-fields' to the list of components passed to the parent PayPalScriptProvider: <PayPalScriptProvider options={{ components: '".concat(a,"'}}>")),i}(((e={components:u.components})[y]=u[y],e)))}))}}),[l]),g?e.createElement(re,null,e.createElement(te.Provider,{value:{cardFieldsForm:E,fields:p,registerField:v,unregisterField:m}},n)):e.createElement("div",null)},ae=function(t){var n=t.className,i=t.fieldName,c=d(t,["className","fieldName"]),l=ne(),u=l.cardFieldsForm,s=l.registerField,f=l.unregisterField,p=r(null),v=o(null)[1];function m(){f(i)}return a((function(){if(!u)return v((function(){throw new Error("Individual CardFields must be rendered inside the PayPalCardFieldsProvider")})),m;if(!p.current)return m;var e=s(i,c,u);return null==e||e.render(p.current).catch((function(e){(function(e){var t;return!!(null===(t=e.current)||void 0===t?void 0:t.children.length)})(p)&&v((function(){throw new Error("Failed to render <PayPal".concat(i," /> component. ").concat(e))}))})),m}),[]),e.createElement("div",{ref:p,className:n})},ie=function(t){return e.createElement(ae,s({fieldName:"NameField"},t))},ce=function(t){return e.createElement(ae,s({fieldName:"NumberField"},t))},le=function(t){return e.createElement(ae,s({fieldName:"ExpiryField"},t))},ue=function(t){return e.createElement(ae,s({fieldName:"CVVField"},t))},se=function(t){var n=t.children;return e.createElement("div",{style:{display:"flex",width:"100%"}},n)},de=function(t){var n=t.className;return e.createElement("div",{className:n},e.createElement(ae,{fieldName:"NameField"}),e.createElement(ae,{fieldName:"NumberField"}),e.createElement(se,null,e.createElement(re,null,e.createElement(ae,{fieldName:"ExpiryField"})),e.createElement(re,null,e.createElement(ae,{fieldName:"CVVField"}))))},fe={PAYPAL:"paypal",VENMO:"venmo",APPLEPAY:"applepay",ITAU:"itau",CREDIT:"credit",PAYLATER:"paylater",CARD:"card",IDEAL:"ideal",SEPA:"sepa",BANCONTACT:"bancontact",GIROPAY:"giropay",SOFORT:"sofort",EPS:"eps",MYBANK:"mybank",P24:"p24",PAYU:"payu",BLIK:"blik",TRUSTLY:"trustly",OXXO:"oxxo",BOLETO:"boleto",BOLETOBANCARIO:"boletobancario",WECHATPAY:"wechatpay",MERCADOPAGO:"mercadopago",MULTIBANCO:"multibanco",SATISPAY:"satispay",PAIDY:"paidy",ZIMPLER:"zimpler",MAXIMA:"maxima"},pe=fe;export{X as BraintreePayPalButtons,l as DISPATCH_ACTION,pe as FUNDING,u as PAYPAL_HOSTED_FIELDS_TYPES,B as PayPalButtons,ue as PayPalCVVField,te as PayPalCardFieldsContext,de as PayPalCardFieldsForm,oe as PayPalCardFieldsProvider,le as PayPalExpiryField,$ as PayPalHostedField,Q as PayPalHostedFieldsProvider,z as PayPalMarks,K as PayPalMessages,ie as PayPalNameField,ce as PayPalNumberField,J as PayPalScriptProvider,c as SCRIPT_LOADING_STATE,F as ScriptContext,C as destroySDKScript,T as getScriptID,R as scriptReducer,ne as usePayPalCardFields,L as usePayPalHostedFields,j as usePayPalScriptReducer,D as useScriptProviderContext};
