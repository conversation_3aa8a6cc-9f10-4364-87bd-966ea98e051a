"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/plugins/page",{

/***/ "(app-pages-browser)/./src/components/PluginCard.tsx":
/*!***************************************!*\
  !*** ./src/components/PluginCard.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PluginCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Download_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Download_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _lib_plugins__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/plugins */ \"(app-pages-browser)/./src/lib/plugins.ts\");\n/* harmony import */ var _lib_cart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/cart */ \"(app-pages-browser)/./src/lib/cart.ts\");\n\n\n\n\n\n\nfunction PluginCard(param) {\n    let { plugin, compact = false } = param;\n    const cardClasses = compact ? \"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200\" : \"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 h-full\";\n    const handleAddToCart = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        (0,_lib_cart__WEBPACK_IMPORTED_MODULE_4__.addToCart)(plugin);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: cardClasses,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/plugins/\".concat(plugin.id),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-48 bg-gray-200 rounded-t-lg flex items-center justify-center\",\n                            children: plugin.imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                src: plugin.imageUrl,\n                                alt: plugin.name,\n                                width: 400,\n                                height: 200,\n                                className: \"w-full h-full object-cover rounded-t-lg\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-rust-100 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-rust-600 font-bold text-xl\",\n                                            children: plugin.name.charAt(0)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"No Image\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        plugin.price === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold\",\n                            children: \"FREE\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/plugins/\".concat(plugin.id),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold text-lg text-gray-900 mb-2 hover:text-rust-600 transition-colors line-clamp-1\",\n                            children: plugin.name\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-sm mb-3 line-clamp-2\",\n                        children: plugin.description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    \"by \",\n                                    plugin.author\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded\",\n                                children: [\n                                    \"v\",\n                                    plugin.version\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4 text-yellow-400 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: plugin.rating\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-4 h-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: (0,_lib_plugins__WEBPACK_IMPORTED_MODULE_3__.formatDownloads)(plugin.downloads)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl font-bold text-rust-600\",\n                                children: (0,_lib_plugins__WEBPACK_IMPORTED_MODULE_3__.formatPrice)(plugin.price)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleAddToCart,\n                                className: \"bg-rust-600 text-white px-4 py-2 rounded-lg hover:bg-rust-700 transition-colors flex items-center text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, this),\n                                    plugin.price === 0 ? \"Download\" : \"Add to Cart\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    !compact && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 flex flex-wrap gap-1\",\n                        children: [\n                            plugin.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs bg-rust-50 text-rust-700 px-2 py-1 rounded\",\n                                    children: tag\n                                }, tag, false, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this)),\n                            plugin.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500\",\n                                children: [\n                                    \"+\",\n                                    plugin.tags.length - 3,\n                                    \" more\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n_c = PluginCard;\nvar _c;\n$RefreshReg$(_c, \"PluginCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PluginCard.tsx\n"));

/***/ })

});