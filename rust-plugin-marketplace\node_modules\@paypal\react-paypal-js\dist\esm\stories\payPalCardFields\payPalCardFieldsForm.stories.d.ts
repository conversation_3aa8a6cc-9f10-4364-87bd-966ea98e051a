import React from "react";
import type { FC } from "react";
declare const _default: {
    title: string;
    component: React.FC<import("../../index").PayPalCardFieldsFormOptions>;
    parameters: {
        controls: {
            expanded: boolean;
            sort: string;
        };
        docs: {
            source: {
                language: string;
            };
        };
    };
    argTypes: {
        className: {
            control: boolean;
            table: {
                category: string;
                type: {
                    summary: string;
                };
            };
            description: string;
            defaultValue: {
                summary: string;
            };
        };
    };
};
export default _default;
export declare const Default: FC;
