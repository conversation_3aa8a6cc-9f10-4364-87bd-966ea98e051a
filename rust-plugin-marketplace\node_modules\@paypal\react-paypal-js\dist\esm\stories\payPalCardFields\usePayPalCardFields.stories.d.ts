import type { FC } from "react";
declare const _default: {
    title: string;
    parameters: {
        controls: {
            expanded: boolean;
            sort: string;
        };
        docs: {
            source: {
                language: string;
            };
            description: {
                component: string;
            };
        };
    };
    argTypes: {
        cardFieldsForm: {
            control: boolean;
            table: {
                category: string;
                type: {
                    summary: string;
                };
            };
            description: string;
        };
        fields: {
            control: boolean;
            table: {
                category: string;
                type: {
                    summary: string;
                };
            };
            description: string;
        };
        FieldComponentName: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
        PayPalCardFieldsIndividualField: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
        PayPalCardFieldsComponent: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
        PayPalCardFieldsStateObjectFields: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
        PayPalCardFieldsStateObject: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
        PayPalCardFieldsCardObject: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
        PayPalCardFieldError: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
        PayPalCardFieldSecurityCode: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
        PayPalCardFieldCardFieldData: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
    };
};
export default _default;
export declare const Default: FC;
