{"version": 3, "sources": ["../../../../../src/server/future/route-modules/helpers/response-handlers.ts"], "names": ["handleRedirectResponse", "handleBadRequestResponse", "handleNotFoundResponse", "handleMethodNotAllowedResponse", "handleInternalServerErrorResponse", "url", "mutableCookies", "status", "headers", "Headers", "location", "appendMutableCookies", "Response"], "mappings": ";;;;;;;;;;;;;;;;;;IAGgBA,sBAAsB;eAAtBA;;IAYAC,wBAAwB;eAAxBA;;IAIAC,sBAAsB;eAAtBA;;IAIAC,8BAA8B;eAA9BA;;IAIAC,iCAAiC;eAAjCA;;;gCA3BqB;AAG9B,SAASJ,uBACdK,GAAW,EACXC,cAA+B,EAC/BC,MAAc;IAEd,MAAMC,UAAU,IAAIC,QAAQ;QAAEC,UAAUL;IAAI;IAE5CM,IAAAA,oCAAoB,EAACH,SAASF;IAE9B,OAAO,IAAIM,SAAS,MAAM;QAAEL;QAAQC;IAAQ;AAC9C;AAEO,SAASP;IACd,OAAO,IAAIW,SAAS,MAAM;QAAEL,QAAQ;IAAI;AAC1C;AAEO,SAASL;IACd,OAAO,IAAIU,SAAS,MAAM;QAAEL,QAAQ;IAAI;AAC1C;AAEO,SAASJ;IACd,OAAO,IAAIS,SAAS,MAAM;QAAEL,QAAQ;IAAI;AAC1C;AAEO,SAASH;IACd,OAAO,IAAIQ,SAAS,MAAM;QAAEL,QAAQ;IAAI;AAC1C"}