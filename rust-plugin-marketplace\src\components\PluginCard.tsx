import Link from 'next/link';
import Image from 'next/image';
import { Star, Download, ShoppingCart } from 'lucide-react';
import { Plugin } from '@/lib/types';
import { formatPrice, formatDownloads } from '@/lib/plugins';
import { addToCart } from '@/lib/cart';

interface PluginCardProps {
  plugin: Plugin;
  compact?: boolean;
}

export default function PluginCard({ plugin, compact = false }: PluginCardProps) {
  const cardClasses = compact
    ? "bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200"
    : "bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 h-full";

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    addToCart(plugin);
  };

  return (
    <div className={cardClasses}>
      <Link href={`/plugins/${plugin.id}`}>
        <div className="relative">
          <div className="w-full h-48 bg-gray-200 rounded-t-lg flex items-center justify-center">
            {plugin.imageUrl ? (
              <Image
                src={plugin.imageUrl}
                alt={plugin.name}
                width={400}
                height={200}
                className="w-full h-full object-cover rounded-t-lg"
              />
            ) : (
              <div className="text-gray-400 text-center">
                <div className="w-16 h-16 bg-rust-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <span className="text-rust-600 font-bold text-xl">
                    {plugin.name.charAt(0)}
                  </span>
                </div>
                <span className="text-sm">No Image</span>
              </div>
            )}
          </div>
          {plugin.price === 0 && (
            <div className="absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold">
              FREE
            </div>
          )}
        </div>
      </Link>

      <div className="p-4">
        <Link href={`/plugins/${plugin.id}`}>
          <h3 className="font-semibold text-lg text-gray-900 mb-2 hover:text-rust-600 transition-colors line-clamp-1">
            {plugin.name}
          </h3>
        </Link>
        
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {plugin.description}
        </p>

        <div className="flex items-center justify-between mb-3">
          <span className="text-sm text-gray-500">by {plugin.author}</span>
          <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
            v{plugin.version}
          </span>
        </div>

        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <div className="flex items-center">
              <Star className="w-4 h-4 text-yellow-400 mr-1" />
              <span>{plugin.rating}</span>
            </div>
            <div className="flex items-center">
              <Download className="w-4 h-4 mr-1" />
              <span>{formatDownloads(plugin.downloads)}</span>
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-xl font-bold text-rust-600">
            {formatPrice(plugin.price)}
          </span>
          <button
            onClick={handleAddToCart}
            className="bg-rust-600 text-white px-4 py-2 rounded-lg hover:bg-rust-700 transition-colors flex items-center text-sm"
          >
            <ShoppingCart className="w-4 h-4 mr-1" />
            {plugin.price === 0 ? 'Download' : 'Add to Cart'}
          </button>
        </div>

        {!compact && (
          <div className="mt-3 flex flex-wrap gap-1">
            {plugin.tags.slice(0, 3).map((tag) => (
              <span
                key={tag}
                className="text-xs bg-rust-50 text-rust-700 px-2 py-1 rounded"
              >
                {tag}
              </span>
            ))}
            {plugin.tags.length > 3 && (
              <span className="text-xs text-gray-500">
                +{plugin.tags.length - 3} more
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
