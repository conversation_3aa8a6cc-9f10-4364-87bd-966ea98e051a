# Plugin Management Guide

This guide explains how to add, update, and manage plugins in your Rust Game Plugin Marketplace.

## Adding a New Plugin

### Step 1: Prepare Plugin Information

Before adding a plugin, gather the following information:
- Plugin name and description
- Author information
- Version number
- Price (in cents, e.g., 2999 for $29.99)
- Category (building, admin, economy, pvp, pve, events, teleportation, utilities)
- Tags for searchability
- Features list
- Requirements (Rust version, dependencies like Oxide/uMod)
- Compatibility (Windows Server, Linux Server)
- File size
- Screenshots and images

### Step 2: Add Plugin to Database

Edit the file `src/data/plugins.json` and add your new plugin entry:

```json
{
  "id": "unique-plugin-id",
  "name": "Your Plugin Name",
  "description": "Short description of your plugin",
  "longDescription": "Detailed description explaining what the plugin does, its benefits, and use cases. This appears on the plugin detail page.",
  "version": "1.0.0",
  "author": "Your Name or Studio",
  "price": 2999,
  "category": "building",
  "tags": ["building", "automation", "base", "tools"],
  "downloadUrl": "/downloads/your-plugin-1.0.0.zip",
  "imageUrl": "/images/plugins/your-plugin.jpg",
  "screenshots": [
    "/images/screenshots/your-plugin-1.jpg",
    "/images/screenshots/your-plugin-2.jpg"
  ],
  "features": [
    "Feature 1 description",
    "Feature 2 description",
    "Feature 3 description"
  ],
  "requirements": {
    "rustVersion": "Latest",
    "dependencies": ["Oxide", "uMod"]
  },
  "compatibility": ["Windows Server", "Linux Server"],
  "lastUpdated": "2024-01-15",
  "downloads": 0,
  "rating": 5.0,
  "reviews": [],
  "changelog": [
    {
      "version": "1.0.0",
      "date": "2024-01-15",
      "changes": [
        "Initial release",
        "Core functionality implemented",
        "Basic configuration options"
      ]
    }
  ],
  "documentation": "https://docs.example.com/your-plugin",
  "sourceCodeUrl": "https://github.com/example/your-plugin",
  "licenseType": "Commercial",
  "fileSize": "2.1 MB"
}
```

### Step 3: Add Plugin Images

1. **Main Plugin Image**: Add a 400x200px image to `public/images/plugins/your-plugin.jpg`
2. **Screenshots**: Add screenshots to `public/images/screenshots/` folder
3. **Recommended image formats**: JPG or PNG
4. **Optimize images** for web to reduce loading times

### Step 4: Upload Plugin Files

1. Create a ZIP file containing your plugin
2. Upload it to `public/downloads/your-plugin-version.zip`
3. Ensure the download URL in the JSON matches the file location

## Plugin Categories

Choose the appropriate category for your plugin:

- **building**: Building tools, automation, base management
- **admin**: Server administration, moderation, management tools
- **economy**: Economy systems, shops, trading, currency
- **pvp**: Player vs Player enhancements, combat systems
- **pve**: Player vs Environment content, NPCs, creatures
- **events**: Custom events, activities, competitions
- **teleportation**: Teleport systems, home commands, travel
- **utilities**: General purpose server utilities

## Updating an Existing Plugin

### Step 1: Update Plugin Information

1. Edit the plugin entry in `src/data/plugins.json`
2. Update the version number
3. Update the `lastUpdated` date
4. Add new changelog entry
5. Update download URL if filename changed

### Step 2: Add Changelog Entry

Add a new entry to the changelog array:

```json
{
  "version": "1.1.0",
  "date": "2024-02-01",
  "changes": [
    "Added new feature X",
    "Fixed bug with Y",
    "Improved performance"
  ]
}
```

### Step 3: Upload New Files

1. Upload the new plugin ZIP file
2. Update any new screenshots or images
3. Test the download link

## Managing Plugin Reviews

Reviews are stored in the `reviews` array of each plugin. To add a review:

```json
{
  "id": "review-unique-id",
  "userId": "user-id",
  "userName": "User Name",
  "rating": 5,
  "comment": "Great plugin! Works perfectly on my server.",
  "date": "2024-01-20"
}
```

## File Structure

```
rust-plugin-marketplace/
├── src/
│   └── data/
│       ├── plugins.json          # Main plugin database
│       └── categories.json       # Category definitions
├── public/
│   ├── images/
│   │   ├── plugins/              # Main plugin images (400x200px)
│   │   └── screenshots/          # Plugin screenshots
│   └── downloads/                # Plugin ZIP files
└── docs/
    ├── DEPLOYMENT.md
    └── PLUGIN_MANAGEMENT.md
```

## Best Practices

### Plugin Information
- Use clear, descriptive names
- Write compelling descriptions that highlight benefits
- Include comprehensive feature lists
- Provide accurate compatibility information
- Keep changelog entries detailed and user-friendly

### Images and Screenshots
- Use high-quality images that showcase your plugin
- Include before/after screenshots when applicable
- Show the plugin in action on actual servers
- Optimize images for web (compress without losing quality)

### Pricing
- Research competitor pricing
- Consider the value your plugin provides
- Price in cents (e.g., 2999 for $29.99)
- Consider offering free plugins to build reputation

### Documentation
- Provide comprehensive documentation
- Include installation instructions
- Add configuration examples
- Create troubleshooting guides

## Testing Your Changes

After adding or updating a plugin:

1. **Start the development server**:
   ```bash
   npm run dev
   ```

2. **Test the plugin appears correctly**:
   - Check the homepage for featured plugins
   - Browse the plugins page
   - Test category filtering
   - Verify the plugin detail page
   - Test the search functionality

3. **Verify all links work**:
   - Download links
   - Image links
   - Documentation links
   - Source code links

4. **Test the purchase flow**:
   - Add to cart functionality
   - Checkout process
   - Payment integration

## Deployment

After making changes to plugins:

1. **Build the application**:
   ```bash
   npm run build
   ```

2. **Deploy to your server** (see DEPLOYMENT.md for details)

3. **Clear any caches** if using CDN or caching

## Troubleshooting

### Common Issues

1. **Plugin not appearing**: Check JSON syntax and file paths
2. **Images not loading**: Verify image files exist and paths are correct
3. **Download links broken**: Ensure ZIP files are uploaded correctly
4. **Build errors**: Check for JSON syntax errors

### Validation

Use online JSON validators to check your `plugins.json` file:
- https://jsonlint.com/
- https://jsonformatter.curiousconcept.com/

## Security Considerations

- **Scan plugin files** for malware before uploading
- **Verify plugin authenticity** from trusted developers
- **Use HTTPS** for all download links
- **Regularly update** plugin files and security patches
- **Monitor downloads** for suspicious activity

## Analytics and Monitoring

Track plugin performance:
- Download counts
- User ratings and reviews
- Popular search terms
- Category performance
- Revenue by plugin

This information helps you understand what plugins are successful and guide future additions to your marketplace.
