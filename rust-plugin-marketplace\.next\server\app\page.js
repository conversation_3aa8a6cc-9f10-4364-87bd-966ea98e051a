/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Csrc%5Clib%5Ccart.ts&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Csrc%5Clib%5Ccart.ts&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/lib/cart.ts */ \"(ssr)/./src/lib/cart.ts\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q1dlYlNpdGVTdHVmZiU1Q3J1c3QtcGx1Z2luLW1hcmtldHBsYWNlJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNpbWFnZS1jb21wb25lbnQuanMmbW9kdWxlcz1EJTNBJTVDV2ViU2l0ZVN0dWZmJTVDcnVzdC1wbHVnaW4tbWFya2V0cGxhY2UlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2xpbmsuanMmbW9kdWxlcz1EJTNBJTVDV2ViU2l0ZVN0dWZmJTVDcnVzdC1wbHVnaW4tbWFya2V0cGxhY2UlNUNzcmMlNUNsaWIlNUNjYXJ0LnRzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzTkFBb0k7QUFDcEksZ01BQXlIO0FBQ3pIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcnVzdC1wbHVnaW4tbWFya2V0cGxhY2UvP2U5ZjUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxXZWJTaXRlU3R1ZmZcXFxccnVzdC1wbHVnaW4tbWFya2V0cGxhY2VcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcaW1hZ2UtY29tcG9uZW50LmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxXZWJTaXRlU3R1ZmZcXFxccnVzdC1wbHVnaW4tbWFya2V0cGxhY2VcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcbGluay5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcV2ViU2l0ZVN0dWZmXFxcXHJ1c3QtcGx1Z2luLW1hcmtldHBsYWNlXFxcXHNyY1xcXFxsaWJcXFxcY2FydC50c1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Csrc%5Clib%5Ccart.ts&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Csrc%5Ccomponents%5CHeader.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Csrc%5Ccomponents%5CHeader.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header.tsx */ \"(ssr)/./src/components/Header.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q1dlYlNpdGVTdHVmZiU1Q3J1c3QtcGx1Z2luLW1hcmtldHBsYWNlJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNsaW5rLmpzJm1vZHVsZXM9RCUzQSU1Q1dlYlNpdGVTdHVmZiU1Q3J1c3QtcGx1Z2luLW1hcmtldHBsYWNlJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2ZvbnQlNUNnb29nbGUlNUN0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMnNyYyU1QyU1Q2FwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmludGVyJTIyJTdEJm1vZHVsZXM9RCUzQSU1Q1dlYlNpdGVTdHVmZiU1Q3J1c3QtcGx1Z2luLW1hcmtldHBsYWNlJTVDc3JjJTVDYXBwJTVDZ2xvYmFscy5jc3MmbW9kdWxlcz1EJTNBJTVDV2ViU2l0ZVN0dWZmJTVDcnVzdC1wbHVnaW4tbWFya2V0cGxhY2UlNUNzcmMlNUNjb21wb25lbnRzJTVDSGVhZGVyLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ01BQXlIO0FBQ3pIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcnVzdC1wbHVnaW4tbWFya2V0cGxhY2UvP2Q4ZDciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxXZWJTaXRlU3R1ZmZcXFxccnVzdC1wbHVnaW4tbWFya2V0cGxhY2VcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcbGluay5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcV2ViU2l0ZVN0dWZmXFxcXHJ1c3QtcGx1Z2luLW1hcmtldHBsYWNlXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXEhlYWRlci50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Csrc%5Ccomponents%5CHeader.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Menu_Search_ShoppingCart_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Search,ShoppingCart,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Search_ShoppingCart_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Search,ShoppingCart,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Search_ShoppingCart_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Search,ShoppingCart,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Search_ShoppingCart_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Search,ShoppingCart,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _lib_cart__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/cart */ \"(ssr)/./src/lib/cart.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Header() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [cartCount, setCartCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setCartCount((0,_lib_cart__WEBPACK_IMPORTED_MODULE_3__.getCartItemCount)());\n        const handleCartUpdate = ()=>{\n            setCartCount((0,_lib_cart__WEBPACK_IMPORTED_MODULE_3__.getCartItemCount)());\n        };\n        window.addEventListener(\"cartUpdated\", handleCartUpdate);\n        return ()=>window.removeEventListener(\"cartUpdated\", handleCartUpdate);\n    }, []);\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        if (searchQuery.trim()) {\n            window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm border-b\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-rust-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-sm\",\n                                        children: \"R\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"Rust Plugins\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex flex-1 max-w-lg mx-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSearch,\n                                className: \"w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Search_ShoppingCart_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Search plugins...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rust-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/plugins\",\n                                    className: \"text-gray-700 hover:text-rust-600 font-medium\",\n                                    children: \"Browse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/categories\",\n                                    className: \"text-gray-700 hover:text-rust-600 font-medium\",\n                                    children: \"Categories\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/about\",\n                                    className: \"text-gray-700 hover:text-rust-600 font-medium\",\n                                    children: \"About\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/cart\",\n                                    className: \"relative p-2 text-gray-700 hover:text-rust-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Search_ShoppingCart_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this),\n                                        cartCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -top-1 -right-1 bg-rust-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\",\n                                            children: cartCount\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                            className: \"md:hidden p-2 text-gray-700\",\n                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Search_ShoppingCart_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 27\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Search_ShoppingCart_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 55\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden pb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSearch,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Search_ShoppingCart_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search plugins...\",\n                                    value: searchQuery,\n                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                    className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rust-500 focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden border-t border-gray-200 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/plugins\",\n                                className: \"text-gray-700 hover:text-rust-600 font-medium\",\n                                children: \"Browse Plugins\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/categories\",\n                                className: \"text-gray-700 hover:text-rust-600 font-medium\",\n                                children: \"Categories\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/about\",\n                                className: \"text-gray-700 hover:text-rust-600 font-medium\",\n                                children: \"About\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/cart\",\n                                className: \"flex items-center space-x-2 text-gray-700 hover:text-rust-600 font-medium\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Search_ShoppingCart_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Cart (\",\n                                            cartCount,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/cart.ts":
/*!*************************!*\
  !*** ./src/lib/cart.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToCart: () => (/* binding */ addToCart),\n/* harmony export */   clearCart: () => (/* binding */ clearCart),\n/* harmony export */   getCart: () => (/* binding */ getCart),\n/* harmony export */   getCartItemCount: () => (/* binding */ getCartItemCount),\n/* harmony export */   getCartTotal: () => (/* binding */ getCartTotal),\n/* harmony export */   removeFromCart: () => (/* binding */ removeFromCart),\n/* harmony export */   updateCartItemQuantity: () => (/* binding */ updateCartItemQuantity)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ getCart,addToCart,removeFromCart,updateCartItemQuantity,clearCart,getCartTotal,getCartItemCount auto */ const CART_STORAGE_KEY = \"rust-plugins-cart\";\nfunction getCart() {\n    if (true) return [];\n    try {\n        const cartData = localStorage.getItem(CART_STORAGE_KEY);\n        return cartData ? JSON.parse(cartData) : [];\n    } catch  {\n        return [];\n    }\n}\nfunction addToCart(plugin) {\n    const cart = getCart();\n    const existingItem = cart.find((item)=>item.plugin.id === plugin.id);\n    if (existingItem) {\n        existingItem.quantity += 1;\n    } else {\n        cart.push({\n            plugin,\n            quantity: 1\n        });\n    }\n    saveCart(cart);\n}\nfunction removeFromCart(pluginId) {\n    const cart = getCart().filter((item)=>item.plugin.id !== pluginId);\n    saveCart(cart);\n}\nfunction updateCartItemQuantity(pluginId, quantity) {\n    const cart = getCart();\n    const item = cart.find((item)=>item.plugin.id === pluginId);\n    if (item) {\n        if (quantity <= 0) {\n            removeFromCart(pluginId);\n        } else {\n            item.quantity = quantity;\n            saveCart(cart);\n        }\n    }\n}\nfunction clearCart() {\n    saveCart([]);\n}\nfunction getCartTotal() {\n    return getCart().reduce((total, item)=>total + item.plugin.price * item.quantity, 0);\n}\nfunction getCartItemCount() {\n    return getCart().reduce((count, item)=>count + item.quantity, 0);\n}\nfunction saveCart(cart) {\n    if (false) {}\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/cart.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"740ee93bb456\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcnVzdC1wbHVnaW4tbWFya2V0cGxhY2UvLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2MyMTQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3NDBlZTkzYmI0NTZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(rsc)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./src/components/Footer.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Rust Plugin Marketplace\",\n    description: \"Discover and purchase high-quality Rust plugins for your projects\",\n    keywords: \"rust, plugins, marketplace, development, tools\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Shield,Star,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Shield,Star,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Shield,Star,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Shield,Star,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _components_PluginCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/PluginCard */ \"(rsc)/./src/components/PluginCard.tsx\");\n/* harmony import */ var _components_CategoryCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/CategoryCard */ \"(rsc)/./src/components/CategoryCard.tsx\");\n/* harmony import */ var _lib_plugins__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/plugins */ \"(rsc)/./src/lib/plugins.ts\");\n\n\n\n\n\n\nfunction HomePage() {\n    const featuredPlugins = (0,_lib_plugins__WEBPACK_IMPORTED_MODULE_4__.getFeaturedPlugins)();\n    const popularPlugins = (0,_lib_plugins__WEBPACK_IMPORTED_MODULE_4__.getPopularPlugins)();\n    const categories = (0,_lib_plugins__WEBPACK_IMPORTED_MODULE_4__.getAllCategories)().slice(0, 6);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-gradient-to-br from-rust-600 to-rust-800 text-white py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-6xl font-bold mb-6\",\n                                children: \"Supercharge Your Rust Development\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl mb-8 text-rust-100 max-w-3xl mx-auto\",\n                                children: \"Discover premium plugins and tools that enhance your Rust projects. From performance optimization to security analysis.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/plugins\",\n                                        className: \"bg-white text-rust-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-flex items-center justify-center\",\n                                        children: [\n                                            \"Browse Plugins\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"ml-2 w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 31,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/categories\",\n                                        className: \"border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-rust-600 transition-colors inline-flex items-center justify-center\",\n                                        children: \"Explore Categories\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"Why Choose Our Marketplace?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                                    children: \"We provide the highest quality Rust plugins with comprehensive support and documentation.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-rust-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-8 h-8 text-rust-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-2\",\n                                            children: \"Secure & Tested\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"All plugins are thoroughly tested and security-reviewed before publication.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-rust-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-8 h-8 text-rust-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-2\",\n                                            children: \"High Performance\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Optimized plugins that enhance your application performance.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-rust-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-8 h-8 text-rust-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-2\",\n                                            children: \"Community Driven\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Built by the community, for the community with continuous updates.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Featured Plugins\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/plugins\",\n                                    className: \"text-rust-600 hover:text-rust-700 font-medium inline-flex items-center\",\n                                    children: [\n                                        \"View All\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"ml-1 w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: featuredPlugins.map((plugin)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PluginCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    plugin: plugin\n                                }, plugin.id, false, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"Browse by Category\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600\",\n                                    children: \"Find plugins organized by functionality and use case.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CategoryCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    category: category\n                                }, category.id, false, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Popular This Month\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/plugins?sort=popular\",\n                                    className: \"text-rust-600 hover:text-rust-700 font-medium inline-flex items-center\",\n                                    children: [\n                                        \"View All\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"ml-1 w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: popularPlugins.slice(0, 4).map((plugin)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PluginCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    plugin: plugin,\n                                    compact: true\n                                }, plugin.id, false, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/CategoryCard.tsx":
/*!*****************************************!*\
  !*** ./src/components/CategoryCard.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CategoryCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Database_Gamepad2_Globe_Network_Shield_Terminal_TestTube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Gamepad2,Globe,Network,Shield,Terminal,TestTube,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Gamepad2_Globe_Network_Shield_Terminal_TestTube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Gamepad2,Globe,Network,Shield,Terminal,TestTube,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Gamepad2_Globe_Network_Shield_Terminal_TestTube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Gamepad2,Globe,Network,Shield,Terminal,TestTube,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/test-tube.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Gamepad2_Globe_Network_Shield_Terminal_TestTube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Gamepad2,Globe,Network,Shield,Terminal,TestTube,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Gamepad2_Globe_Network_Shield_Terminal_TestTube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Gamepad2,Globe,Network,Shield,Terminal,TestTube,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Gamepad2_Globe_Network_Shield_Terminal_TestTube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Gamepad2,Globe,Network,Shield,Terminal,TestTube,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Gamepad2_Globe_Network_Shield_Terminal_TestTube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Gamepad2,Globe,Network,Shield,Terminal,TestTube,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/network.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Gamepad2_Globe_Network_Shield_Terminal_TestTube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Gamepad2,Globe,Network,Shield,Terminal,TestTube,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/gamepad-2.js\");\n/* harmony import */ var _lib_plugins__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/plugins */ \"(rsc)/./src/lib/plugins.ts\");\n\n\n\n\nconst iconMap = {\n    Zap: _barrel_optimize_names_Database_Gamepad2_Globe_Network_Shield_Terminal_TestTube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    Shield: _barrel_optimize_names_Database_Gamepad2_Globe_Network_Shield_Terminal_TestTube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    TestTube: _barrel_optimize_names_Database_Gamepad2_Globe_Network_Shield_Terminal_TestTube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    Globe: _barrel_optimize_names_Database_Gamepad2_Globe_Network_Shield_Terminal_TestTube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    Terminal: _barrel_optimize_names_Database_Gamepad2_Globe_Network_Shield_Terminal_TestTube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    Database: _barrel_optimize_names_Database_Gamepad2_Globe_Network_Shield_Terminal_TestTube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    Network: _barrel_optimize_names_Database_Gamepad2_Globe_Network_Shield_Terminal_TestTube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    Gamepad2: _barrel_optimize_names_Database_Gamepad2_Globe_Network_Shield_Terminal_TestTube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n};\nfunction CategoryCard({ category }) {\n    const IconComponent = iconMap[category.icon] || _barrel_optimize_names_Database_Gamepad2_Globe_Network_Shield_Terminal_TestTube_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n    const pluginCount = (0,_lib_plugins__WEBPACK_IMPORTED_MODULE_2__.getPluginsByCategory)(category.id).length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n        href: `/categories/${category.id}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200 p-6 h-full group\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-rust-100 w-12 h-12 rounded-lg flex items-center justify-center mr-4 group-hover:bg-rust-200 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                className: \"w-6 h-6 text-rust-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\CategoryCard.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\CategoryCard.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-lg text-gray-900 group-hover:text-rust-600 transition-colors\",\n                                    children: category.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\CategoryCard.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: [\n                                        pluginCount,\n                                        \" plugin\",\n                                        pluginCount !== 1 ? \"s\" : \"\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\CategoryCard.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\CategoryCard.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\CategoryCard.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 text-sm\",\n                    children: category.description\n                }, void 0, false, {\n                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\CategoryCard.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\CategoryCard.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\CategoryCard.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/CategoryCard.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Github_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Mail,Twitter!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Mail,Twitter!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Mail,Twitter!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-1 md:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-rust-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-sm\",\n                                                children: \"R\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 13,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 12,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold\",\n                                            children: \"Rust Plugins\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 15,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 11,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4 max-w-md\",\n                                    children: \"The premier marketplace for high-quality Rust plugins. Discover tools that enhance your Rust development experience.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 22,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 21,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 25,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 28,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/plugins\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Browse Plugins\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 38,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/categories\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Categories\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/about\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"About Us\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/contact\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Contact\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Support\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/help\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Help Center\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/docs\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Documentation\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/privacy\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/terms\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Terms of Service\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"\\xa9 2024 Rust Plugin Marketplace. All rights reserved.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\Footer.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\WebSiteStuff\rust-plugin-marketplace\src\components\Header.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/PluginCard.tsx":
/*!***************************************!*\
  !*** ./src/components/PluginCard.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PluginCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Download_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,ShoppingCart,Star!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Download_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,ShoppingCart,Star!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,ShoppingCart,Star!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _lib_plugins__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/plugins */ \"(rsc)/./src/lib/plugins.ts\");\n/* harmony import */ var _lib_cart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/cart */ \"(rsc)/./src/lib/cart.ts\");\n\n\n\n\n\n\nfunction PluginCard({ plugin, compact = false }) {\n    const cardClasses = compact ? \"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200\" : \"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 h-full\";\n    const handleAddToCart = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        (0,_lib_cart__WEBPACK_IMPORTED_MODULE_4__.addToCart)(plugin);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: cardClasses,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: `/plugins/${plugin.id}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-48 bg-gray-200 rounded-t-lg flex items-center justify-center\",\n                            children: plugin.imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                src: plugin.imageUrl,\n                                alt: plugin.name,\n                                width: 400,\n                                height: 200,\n                                className: \"w-full h-full object-cover rounded-t-lg\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-rust-100 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-rust-600 font-bold text-xl\",\n                                            children: plugin.name.charAt(0)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"No Image\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        plugin.price === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold\",\n                            children: \"FREE\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: `/plugins/${plugin.id}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold text-lg text-gray-900 mb-2 hover:text-rust-600 transition-colors line-clamp-1\",\n                            children: plugin.name\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-sm mb-3 line-clamp-2\",\n                        children: plugin.description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    \"by \",\n                                    plugin.author\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded\",\n                                children: [\n                                    \"v\",\n                                    plugin.version\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4 text-yellow-400 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: plugin.rating\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-4 h-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: (0,_lib_plugins__WEBPACK_IMPORTED_MODULE_3__.formatDownloads)(plugin.downloads)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl font-bold text-rust-600\",\n                                children: (0,_lib_plugins__WEBPACK_IMPORTED_MODULE_3__.formatPrice)(plugin.price)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleAddToCart,\n                                className: \"bg-rust-600 text-white px-4 py-2 rounded-lg hover:bg-rust-700 transition-colors flex items-center text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, this),\n                                    plugin.price === 0 ? \"Download\" : \"Add to Cart\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    !compact && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 flex flex-wrap gap-1\",\n                        children: [\n                            plugin.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs bg-rust-50 text-rust-700 px-2 py-1 rounded\",\n                                    children: tag\n                                }, tag, false, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this)),\n                            plugin.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500\",\n                                children: [\n                                    \"+\",\n                                    plugin.tags.length - 3,\n                                    \" more\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9QbHVnaW5DYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQTZCO0FBQ0U7QUFDNkI7QUFFQztBQUN0QjtBQU94QixTQUFTUSxXQUFXLEVBQUVDLE1BQU0sRUFBRUMsVUFBVSxLQUFLLEVBQW1CO0lBQzdFLE1BQU1DLGNBQWNELFVBQ2hCLGlGQUNBO0lBRUosTUFBTUUsa0JBQWtCLENBQUNDO1FBQ3ZCQSxFQUFFQyxjQUFjO1FBQ2hCRCxFQUFFRSxlQUFlO1FBQ2pCUixvREFBU0EsQ0FBQ0U7SUFDWjtJQUVBLHFCQUNFLDhEQUFDTztRQUFJQyxXQUFXTjs7MEJBQ2QsOERBQUNYLGtEQUFJQTtnQkFBQ2tCLE1BQU0sQ0FBQyxTQUFTLEVBQUVULE9BQU9VLEVBQUUsQ0FBQyxDQUFDOzBCQUNqQyw0RUFBQ0g7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDWlIsT0FBT1csUUFBUSxpQkFDZCw4REFBQ25CLG1EQUFLQTtnQ0FDSm9CLEtBQUtaLE9BQU9XLFFBQVE7Z0NBQ3BCRSxLQUFLYixPQUFPYyxJQUFJO2dDQUNoQkMsT0FBTztnQ0FDUEMsUUFBUTtnQ0FDUlIsV0FBVTs7Ozs7cURBR1osOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNTOzRDQUFLVCxXQUFVO3NEQUNiUixPQUFPYyxJQUFJLENBQUNJLE1BQU0sQ0FBQzs7Ozs7Ozs7Ozs7a0RBR3hCLDhEQUFDRDt3Q0FBS1QsV0FBVTtrREFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7d0JBSS9CUixPQUFPbUIsS0FBSyxLQUFLLG1CQUNoQiw4REFBQ1o7NEJBQUlDLFdBQVU7c0NBQXlGOzs7Ozs7Ozs7Ozs7Ozs7OzswQkFPOUcsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ2pCLGtEQUFJQTt3QkFBQ2tCLE1BQU0sQ0FBQyxTQUFTLEVBQUVULE9BQU9VLEVBQUUsQ0FBQyxDQUFDO2tDQUNqQyw0RUFBQ1U7NEJBQUdaLFdBQVU7c0NBQ1hSLE9BQU9jLElBQUk7Ozs7Ozs7Ozs7O2tDQUloQiw4REFBQ087d0JBQUViLFdBQVU7a0NBQ1ZSLE9BQU9zQixXQUFXOzs7Ozs7a0NBR3JCLDhEQUFDZjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNTO2dDQUFLVCxXQUFVOztvQ0FBd0I7b0NBQUlSLE9BQU91QixNQUFNOzs7Ozs7OzBDQUN6RCw4REFBQ047Z0NBQUtULFdBQVU7O29DQUFzRDtvQ0FDbEVSLE9BQU93QixPQUFPOzs7Ozs7Ozs7Ozs7O2tDQUlwQiw4REFBQ2pCO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ2Ysc0dBQUlBOzRDQUFDZSxXQUFVOzs7Ozs7c0RBQ2hCLDhEQUFDUztzREFBTWpCLE9BQU95QixNQUFNOzs7Ozs7Ozs7Ozs7OENBRXRCLDhEQUFDbEI7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDZCxzR0FBUUE7NENBQUNjLFdBQVU7Ozs7OztzREFDcEIsOERBQUNTO3NEQUFNcEIsNkRBQWVBLENBQUNHLE9BQU8wQixTQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLN0MsOERBQUNuQjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNTO2dDQUFLVCxXQUFVOzBDQUNiWix5REFBV0EsQ0FBQ0ksT0FBT21CLEtBQUs7Ozs7OzswQ0FFM0IsOERBQUNRO2dDQUNDQyxTQUFTekI7Z0NBQ1RLLFdBQVU7O2tEQUVWLDhEQUFDYixzR0FBWUE7d0NBQUNhLFdBQVU7Ozs7OztvQ0FDdkJSLE9BQU9tQixLQUFLLEtBQUssSUFBSSxhQUFhOzs7Ozs7Ozs7Ozs7O29CQUl0QyxDQUFDbEIseUJBQ0EsOERBQUNNO3dCQUFJQyxXQUFVOzs0QkFDWlIsT0FBTzZCLElBQUksQ0FBQ0MsS0FBSyxDQUFDLEdBQUcsR0FBR0MsR0FBRyxDQUFDLENBQUNDLG9CQUM1Qiw4REFBQ2Y7b0NBRUNULFdBQVU7OENBRVR3QjttQ0FISUE7Ozs7OzRCQU1SaEMsT0FBTzZCLElBQUksQ0FBQ0ksTUFBTSxHQUFHLG1CQUNwQiw4REFBQ2hCO2dDQUFLVCxXQUFVOztvQ0FBd0I7b0NBQ3BDUixPQUFPNkIsSUFBSSxDQUFDSSxNQUFNLEdBQUc7b0NBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRekMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ydXN0LXBsdWdpbi1tYXJrZXRwbGFjZS8uL3NyYy9jb21wb25lbnRzL1BsdWdpbkNhcmQudHN4PzRiZGQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJztcbmltcG9ydCBJbWFnZSBmcm9tICduZXh0L2ltYWdlJztcbmltcG9ydCB7IFN0YXIsIERvd25sb2FkLCBTaG9wcGluZ0NhcnQgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgUGx1Z2luIH0gZnJvbSAnQC9saWIvdHlwZXMnO1xuaW1wb3J0IHsgZm9ybWF0UHJpY2UsIGZvcm1hdERvd25sb2FkcyB9IGZyb20gJ0AvbGliL3BsdWdpbnMnO1xuaW1wb3J0IHsgYWRkVG9DYXJ0IH0gZnJvbSAnQC9saWIvY2FydCc7XG5cbmludGVyZmFjZSBQbHVnaW5DYXJkUHJvcHMge1xuICBwbHVnaW46IFBsdWdpbjtcbiAgY29tcGFjdD86IGJvb2xlYW47XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBsdWdpbkNhcmQoeyBwbHVnaW4sIGNvbXBhY3QgPSBmYWxzZSB9OiBQbHVnaW5DYXJkUHJvcHMpIHtcbiAgY29uc3QgY2FyZENsYXNzZXMgPSBjb21wYWN0XG4gICAgPyBcImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LW1kIGhvdmVyOnNoYWRvdy1sZyB0cmFuc2l0aW9uLXNoYWRvdyBkdXJhdGlvbi0yMDBcIlxuICAgIDogXCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1tZCBob3ZlcjpzaGFkb3ctbGcgdHJhbnNpdGlvbi1zaGFkb3cgZHVyYXRpb24tMjAwIGgtZnVsbFwiO1xuXG4gIGNvbnN0IGhhbmRsZUFkZFRvQ2FydCA9IChlOiBSZWFjdC5Nb3VzZUV2ZW50KSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgYWRkVG9DYXJ0KHBsdWdpbik7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17Y2FyZENsYXNzZXN9PlxuICAgICAgPExpbmsgaHJlZj17YC9wbHVnaW5zLyR7cGx1Z2luLmlkfWB9PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgaC00OCBiZy1ncmF5LTIwMCByb3VuZGVkLXQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgIHtwbHVnaW4uaW1hZ2VVcmwgPyAoXG4gICAgICAgICAgICAgIDxJbWFnZVxuICAgICAgICAgICAgICAgIHNyYz17cGx1Z2luLmltYWdlVXJsfVxuICAgICAgICAgICAgICAgIGFsdD17cGx1Z2luLm5hbWV9XG4gICAgICAgICAgICAgICAgd2lkdGg9ezQwMH1cbiAgICAgICAgICAgICAgICBoZWlnaHQ9ezIwMH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb3ZlciByb3VuZGVkLXQtbGdcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgYmctcnVzdC0xMDAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcnVzdC02MDAgZm9udC1ib2xkIHRleHQteGxcIj5cbiAgICAgICAgICAgICAgICAgICAge3BsdWdpbi5uYW1lLmNoYXJBdCgwKX1cbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+Tm8gSW1hZ2U8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICB7cGx1Z2luLnByaWNlID09PSAwICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTIgcmlnaHQtMiBiZy1ncmVlbi01MDAgdGV4dC13aGl0ZSBweC0yIHB5LTEgcm91bmRlZCB0ZXh0LXhzIGZvbnQtc2VtaWJvbGRcIj5cbiAgICAgICAgICAgICAgRlJFRVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L0xpbms+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00XCI+XG4gICAgICAgIDxMaW5rIGhyZWY9e2AvcGx1Z2lucy8ke3BsdWdpbi5pZH1gfT5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWxnIHRleHQtZ3JheS05MDAgbWItMiBob3Zlcjp0ZXh0LXJ1c3QtNjAwIHRyYW5zaXRpb24tY29sb3JzIGxpbmUtY2xhbXAtMVwiPlxuICAgICAgICAgICAge3BsdWdpbi5uYW1lfVxuICAgICAgICAgIDwvaDM+XG4gICAgICAgIDwvTGluaz5cbiAgICAgICAgXG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgdGV4dC1zbSBtYi0zIGxpbmUtY2xhbXAtMlwiPlxuICAgICAgICAgIHtwbHVnaW4uZGVzY3JpcHRpb259XG4gICAgICAgIDwvcD5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi0zXCI+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+Ynkge3BsdWdpbi5hdXRob3J9PC9zcGFuPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgYmctZ3JheS0xMDAgdGV4dC1ncmF5LTcwMCBweC0yIHB5LTEgcm91bmRlZFwiPlxuICAgICAgICAgICAgdntwbHVnaW4udmVyc2lvbn1cbiAgICAgICAgICA8L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNCB0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPFN0YXIgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXllbGxvdy00MDAgbXItMVwiIC8+XG4gICAgICAgICAgICAgIDxzcGFuPntwbHVnaW4ucmF0aW5nfTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8RG93bmxvYWQgY2xhc3NOYW1lPVwidy00IGgtNCBtci0xXCIgLz5cbiAgICAgICAgICAgICAgPHNwYW4+e2Zvcm1hdERvd25sb2FkcyhwbHVnaW4uZG93bmxvYWRzKX08L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LXJ1c3QtNjAwXCI+XG4gICAgICAgICAgICB7Zm9ybWF0UHJpY2UocGx1Z2luLnByaWNlKX1cbiAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQWRkVG9DYXJ0fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctcnVzdC02MDAgdGV4dC13aGl0ZSBweC00IHB5LTIgcm91bmRlZC1sZyBob3ZlcjpiZy1ydXN0LTcwMCB0cmFuc2l0aW9uLWNvbG9ycyBmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LXNtXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8U2hvcHBpbmdDYXJ0IGNsYXNzTmFtZT1cInctNCBoLTQgbXItMVwiIC8+XG4gICAgICAgICAgICB7cGx1Z2luLnByaWNlID09PSAwID8gJ0Rvd25sb2FkJyA6ICdBZGQgdG8gQ2FydCd9XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHshY29tcGFjdCAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0zIGZsZXggZmxleC13cmFwIGdhcC0xXCI+XG4gICAgICAgICAgICB7cGx1Z2luLnRhZ3Muc2xpY2UoMCwgMykubWFwKCh0YWcpID0+IChcbiAgICAgICAgICAgICAgPHNwYW5cbiAgICAgICAgICAgICAgICBrZXk9e3RhZ31cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXhzIGJnLXJ1c3QtNTAgdGV4dC1ydXN0LTcwMCBweC0yIHB5LTEgcm91bmRlZFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7dGFnfVxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICAgIHtwbHVnaW4udGFncy5sZW5ndGggPiAzICYmIChcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgK3twbHVnaW4udGFncy5sZW5ndGggLSAzfSBtb3JlXG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJMaW5rIiwiSW1hZ2UiLCJTdGFyIiwiRG93bmxvYWQiLCJTaG9wcGluZ0NhcnQiLCJmb3JtYXRQcmljZSIsImZvcm1hdERvd25sb2FkcyIsImFkZFRvQ2FydCIsIlBsdWdpbkNhcmQiLCJwbHVnaW4iLCJjb21wYWN0IiwiY2FyZENsYXNzZXMiLCJoYW5kbGVBZGRUb0NhcnQiLCJlIiwicHJldmVudERlZmF1bHQiLCJzdG9wUHJvcGFnYXRpb24iLCJkaXYiLCJjbGFzc05hbWUiLCJocmVmIiwiaWQiLCJpbWFnZVVybCIsInNyYyIsImFsdCIsIm5hbWUiLCJ3aWR0aCIsImhlaWdodCIsInNwYW4iLCJjaGFyQXQiLCJwcmljZSIsImgzIiwicCIsImRlc2NyaXB0aW9uIiwiYXV0aG9yIiwidmVyc2lvbiIsInJhdGluZyIsImRvd25sb2FkcyIsImJ1dHRvbiIsIm9uQ2xpY2siLCJ0YWdzIiwic2xpY2UiLCJtYXAiLCJ0YWciLCJsZW5ndGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/components/PluginCard.tsx\n");

/***/ }),

/***/ "(rsc)/./src/lib/cart.ts":
/*!*************************!*\
  !*** ./src/lib/cart.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   addToCart: () => (/* binding */ e1),
/* harmony export */   clearCart: () => (/* binding */ e4),
/* harmony export */   getCart: () => (/* binding */ e0),
/* harmony export */   getCartItemCount: () => (/* binding */ e6),
/* harmony export */   getCartTotal: () => (/* binding */ e5),
/* harmony export */   removeFromCart: () => (/* binding */ e2),
/* harmony export */   updateCartItemQuantity: () => (/* binding */ e3)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\WebSiteStuff\rust-plugin-marketplace\src\lib\cart.ts`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\WebSiteStuff\rust-plugin-marketplace\src\lib\cart.ts#getCart`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\WebSiteStuff\rust-plugin-marketplace\src\lib\cart.ts#addToCart`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\WebSiteStuff\rust-plugin-marketplace\src\lib\cart.ts#removeFromCart`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\WebSiteStuff\rust-plugin-marketplace\src\lib\cart.ts#updateCartItemQuantity`);

const e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\WebSiteStuff\rust-plugin-marketplace\src\lib\cart.ts#clearCart`);

const e5 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\WebSiteStuff\rust-plugin-marketplace\src\lib\cart.ts#getCartTotal`);

const e6 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\WebSiteStuff\rust-plugin-marketplace\src\lib\cart.ts#getCartItemCount`);


/***/ }),

/***/ "(rsc)/./src/lib/plugins.ts":
/*!****************************!*\
  !*** ./src/lib/plugins.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDownloads: () => (/* binding */ formatDownloads),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   getAllCategories: () => (/* binding */ getAllCategories),\n/* harmony export */   getAllPlugins: () => (/* binding */ getAllPlugins),\n/* harmony export */   getCategoryById: () => (/* binding */ getCategoryById),\n/* harmony export */   getFeaturedPlugins: () => (/* binding */ getFeaturedPlugins),\n/* harmony export */   getPluginById: () => (/* binding */ getPluginById),\n/* harmony export */   getPluginsByCategory: () => (/* binding */ getPluginsByCategory),\n/* harmony export */   getPluginsByTags: () => (/* binding */ getPluginsByTags),\n/* harmony export */   getPopularPlugins: () => (/* binding */ getPopularPlugins),\n/* harmony export */   getRecentPlugins: () => (/* binding */ getRecentPlugins),\n/* harmony export */   searchPlugins: () => (/* binding */ searchPlugins)\n/* harmony export */ });\n/* harmony import */ var _data_plugins_json__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../data/plugins.json */ \"(rsc)/./src/data/plugins.json\");\n/* harmony import */ var _data_categories_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../data/categories.json */ \"(rsc)/./src/data/categories.json\");\n\n\nfunction getAllPlugins() {\n    return _data_plugins_json__WEBPACK_IMPORTED_MODULE_0__;\n}\nfunction getPluginById(id) {\n    return getAllPlugins().find((plugin)=>plugin.id === id);\n}\nfunction getPluginsByCategory(categoryId) {\n    return getAllPlugins().filter((plugin)=>plugin.category === categoryId);\n}\nfunction searchPlugins(query) {\n    const lowercaseQuery = query.toLowerCase();\n    return getAllPlugins().filter((plugin)=>plugin.name.toLowerCase().includes(lowercaseQuery) || plugin.description.toLowerCase().includes(lowercaseQuery) || plugin.tags.some((tag)=>tag.toLowerCase().includes(lowercaseQuery)) || plugin.author.toLowerCase().includes(lowercaseQuery));\n}\nfunction getPluginsByTags(tags) {\n    return getAllPlugins().filter((plugin)=>tags.some((tag)=>plugin.tags.includes(tag)));\n}\nfunction getFeaturedPlugins() {\n    // Return plugins with high ratings and downloads\n    return getAllPlugins().filter((plugin)=>plugin.rating >= 4.5 && plugin.downloads >= 500).sort((a, b)=>b.downloads - a.downloads).slice(0, 6);\n}\nfunction getPopularPlugins() {\n    return getAllPlugins().sort((a, b)=>b.downloads - a.downloads).slice(0, 8);\n}\nfunction getRecentPlugins() {\n    return getAllPlugins().sort((a, b)=>new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime()).slice(0, 8);\n}\nfunction getAllCategories() {\n    return _data_categories_json__WEBPACK_IMPORTED_MODULE_1__;\n}\nfunction getCategoryById(id) {\n    return getAllCategories().find((category)=>category.id === id);\n}\nfunction formatPrice(priceInCents) {\n    if (priceInCents === 0) return \"Free\";\n    return `$${(priceInCents / 100).toFixed(2)}`;\n}\nfunction formatFileSize(size) {\n    return size;\n}\nfunction formatDownloads(downloads) {\n    if (downloads >= 1000000) {\n        return `${(downloads / 1000000).toFixed(1)}M`;\n    } else if (downloads >= 1000) {\n        return `${(downloads / 1000).toFixed(1)}K`;\n    }\n    return downloads.toString();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/plugins.ts\n");

/***/ }),

/***/ "(rsc)/./src/data/categories.json":
/*!**********************************!*\
  !*** ./src/data/categories.json ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = JSON.parse('[{"id":"performance","name":"Performance","description":"Tools and plugins for optimizing Rust application performance","icon":"Zap"},{"id":"security","name":"Security","description":"Security analysis and vulnerability detection tools","icon":"Shield"},{"id":"testing","name":"Testing","description":"Testing frameworks and utilities for Rust development","icon":"TestTube"},{"id":"web","name":"Web Development","description":"Web frameworks and tools for Rust web development","icon":"Globe"},{"id":"cli","name":"CLI Tools","description":"Command-line interface tools and utilities","icon":"Terminal"},{"id":"database","name":"Database","description":"Database drivers and ORM tools for Rust","icon":"Database"},{"id":"networking","name":"Networking","description":"Network programming and communication tools","icon":"Network"},{"id":"graphics","name":"Graphics & Game Dev","description":"Graphics programming and game development tools","icon":"Gamepad2"}]');

/***/ }),

/***/ "(rsc)/./src/data/plugins.json":
/*!*******************************!*\
  !*** ./src/data/plugins.json ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = JSON.parse('[{"id":"rust-performance-optimizer","name":"Rust Performance Optimizer","description":"Advanced performance optimization plugin for Rust applications","longDescription":"This plugin provides comprehensive performance optimization tools for Rust applications, including memory usage analysis, CPU profiling, and automated optimization suggestions. Perfect for production applications that need to squeeze every bit of performance.","version":"2.1.0","author":"RustDev Studios","price":2999,"category":"performance","tags":["performance","optimization","profiling","memory"],"downloadUrl":"/downloads/rust-performance-optimizer-2.1.0.zip","imageUrl":"/images/plugins/performance-optimizer.jpg","screenshots":["/images/screenshots/perf-opt-1.jpg","/images/screenshots/perf-opt-2.jpg"],"features":["Real-time performance monitoring","Memory leak detection","CPU usage optimization","Automated code suggestions","Benchmark integration"],"requirements":{"rustVersion":"1.70.0+","dependencies":["tokio","serde"]},"compatibility":["Windows","Linux","macOS"],"lastUpdated":"2024-01-15","downloads":1250,"rating":4.8,"reviews":[],"changelog":[{"version":"2.1.0","date":"2024-01-15","changes":["Added async performance monitoring","Improved memory analysis accuracy","Fixed compatibility issues with Rust 1.75"]}],"documentation":"https://docs.example.com/performance-optimizer","sourceCodeUrl":"https://github.com/example/rust-performance-optimizer","licenseType":"MIT","fileSize":"2.3 MB"},{"id":"rust-security-scanner","name":"Rust Security Scanner","description":"Comprehensive security analysis and vulnerability detection for Rust code","longDescription":"A powerful security scanner that analyzes your Rust code for potential vulnerabilities, unsafe patterns, and security best practices. Includes integration with popular security databases and provides detailed remediation guidance.","version":"1.5.2","author":"SecureRust Team","price":3999,"category":"security","tags":["security","vulnerability","analysis","safety"],"downloadUrl":"/downloads/rust-security-scanner-1.5.2.zip","imageUrl":"/images/plugins/security-scanner.jpg","screenshots":["/images/screenshots/sec-scan-1.jpg","/images/screenshots/sec-scan-2.jpg"],"features":["Vulnerability database integration","Unsafe code pattern detection","Dependency security analysis","Compliance reporting","Real-time scanning"],"requirements":{"rustVersion":"1.65.0+","dependencies":["clap","regex"]},"compatibility":["Windows","Linux","macOS"],"lastUpdated":"2024-01-10","downloads":890,"rating":4.9,"reviews":[],"changelog":[{"version":"1.5.2","date":"2024-01-10","changes":["Updated vulnerability database","Added new unsafe pattern detection","Performance improvements"]}],"documentation":"https://docs.example.com/security-scanner","licenseType":"Commercial","fileSize":"1.8 MB"}]');

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();