'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Trash2, Plus, Minus, ShoppingBag, CreditCard } from 'lucide-react';
import { getCart, removeFromCart, updateCartItemQuantity, getCartTotal, clearCart } from '@/lib/cart';
import { formatPrice } from '@/lib/plugins';
import { CartItem } from '@/lib/types';

export default function CartPage() {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    setCartItems(getCart());
    setIsLoading(false);

    const handleCartUpdate = () => {
      setCartItems(getCart());
    };

    window.addEventListener('cartUpdated', handleCartUpdate);
    return () => window.removeEventListener('cartUpdated', handleCartUpdate);
  }, []);

  const handleQuantityChange = (pluginId: string, newQuantity: number) => {
    updateCartItemQuantity(pluginId, newQuantity);
  };

  const handleRemoveItem = (pluginId: string) => {
    removeFromCart(pluginId);
  };

  const handleClearCart = () => {
    clearCart();
  };

  const total = getCartTotal();

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (cartItems.length === 0) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <ShoppingBag className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Your cart is empty</h1>
          <p className="text-gray-600 mb-6">
            Discover amazing Rust plugins to enhance your development workflow.
          </p>
          <Link
            href="/plugins"
            className="bg-rust-600 text-white px-6 py-3 rounded-lg hover:bg-rust-700 transition-colors inline-flex items-center"
          >
            Browse Plugins
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Shopping Cart</h1>
        <button
          onClick={handleClearCart}
          className="text-red-600 hover:text-red-700 text-sm font-medium"
        >
          Clear Cart
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Cart Items */}
        <div className="lg:col-span-2">
          <div className="space-y-4">
            {cartItems.map((item) => (
              <div key={item.plugin.id} className="bg-white rounded-lg shadow-sm border p-4">
                <div className="flex items-center space-x-4">
                  {/* Plugin Image */}
                  <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span className="text-rust-600 font-bold text-lg">
                      {item.plugin.name.charAt(0)}
                    </span>
                  </div>

                  {/* Plugin Info */}
                  <div className="flex-1 min-w-0">
                    <Link
                      href={`/plugins/${item.plugin.id}`}
                      className="text-lg font-semibold text-gray-900 hover:text-rust-600 transition-colors"
                    >
                      {item.plugin.name}
                    </Link>
                    <p className="text-gray-600 text-sm mt-1">{item.plugin.description}</p>
                    <p className="text-sm text-gray-500 mt-1">by {item.plugin.author}</p>
                  </div>

                  {/* Quantity Controls */}
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleQuantityChange(item.plugin.id, item.quantity - 1)}
                      className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                      disabled={item.quantity <= 1}
                    >
                      <Minus className="w-4 h-4" />
                    </button>
                    <span className="w-8 text-center font-medium">{item.quantity}</span>
                    <button
                      onClick={() => handleQuantityChange(item.plugin.id, item.quantity + 1)}
                      className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>

                  {/* Price */}
                  <div className="text-right">
                    <div className="text-lg font-semibold text-gray-900">
                      {formatPrice(item.plugin.price * item.quantity)}
                    </div>
                    {item.quantity > 1 && (
                      <div className="text-sm text-gray-500">
                        {formatPrice(item.plugin.price)} each
                      </div>
                    )}
                  </div>

                  {/* Remove Button */}
                  <button
                    onClick={() => handleRemoveItem(item.plugin.id)}
                    className="text-red-600 hover:text-red-700 p-2"
                  >
                    <Trash2 className="w-5 h-5" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Order Summary */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-sm border p-6 sticky top-8">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h2>
            
            <div className="space-y-3 mb-6">
              <div className="flex justify-between">
                <span className="text-gray-600">Subtotal</span>
                <span className="font-medium">{formatPrice(total)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Tax</span>
                <span className="font-medium">$0.00</span>
              </div>
              <div className="border-t pt-3">
                <div className="flex justify-between">
                  <span className="text-lg font-semibold">Total</span>
                  <span className="text-lg font-semibold text-rust-600">{formatPrice(total)}</span>
                </div>
              </div>
            </div>

            <button className="w-full bg-rust-600 text-white py-3 px-4 rounded-lg hover:bg-rust-700 transition-colors font-semibold flex items-center justify-center mb-3">
              <CreditCard className="w-5 h-5 mr-2" />
              Proceed to Checkout
            </button>

            <Link
              href="/plugins"
              className="w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-50 transition-colors font-medium flex items-center justify-center"
            >
              Continue Shopping
            </Link>

            <div className="mt-6 text-xs text-gray-500">
              <p>• Secure payment processing</p>
              <p>• Instant download after purchase</p>
              <p>• 30-day money-back guarantee</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
