/* @flow */

export const SDK_PATH = ("/sdk/js": "/sdk/js");
export const WEB_SDK_BRIDGE_PATH = ("/web-sdk/v6/bridge": "/web-sdk/v6/bridge");

export const SDK_SETTINGS = {
  AMOUNT: ("data-amount": "data-amount"),
  API_STAGE_HOST: ("data-api-stage-host": "data-api-stage-host"),
  CLIENT_METADATA_ID: ("data-client-metadata-id": "data-client-metadata-id"),
  CLIENT_TOKEN: ("data-client-token": "data-client-token"),
  CSP_NONCE: ("data-csp-nonce": "data-csp-nonce"),
  ENABLE_3DS: ("data-enable-3ds": "data-enable-3ds"),
  JS_SDK_LIBRARY: ("data-js-sdk-library": "data-js-sdk-library"),
  MERCHANT_ID: ("data-merchant-id": "data-merchant-id"),
  NAMESPACE: ("data-namespace": "data-namespace"),
  PAGE_TYPE: ("data-page-type": "data-page-type"),
  PARTNER_ATTRIBUTION_ID:
    ("data-partner-attribution-id": "data-partner-attribution-id"),
  POPUPS_DISABLED: ("data-popups-disabled": "data-popups-disabled"),
  SDK_INTEGRATION_SOURCE:
    ("data-sdk-integration-source": "data-sdk-integration-source"),
  SDK_TOKEN: ("data-sdk-client-token": "data-sdk-client-token"),
  SHOPPER_SESSION_ID: ("data-shopper-session-id": "data-shopper-session-id"),
  STAGE_HOST: ("data-stage-host": "data-stage-host"),
  USER_EXPERIENCE_FLOW:
    ("data-user-experience-flow": "data-user-experience-flow"),
  USER_ID_TOKEN: ("data-user-id-token": "data-user-id-token"),
};

// Why do we call these settings instead of what they are, data attributes?
// all other constants in this file are named after what they are
export const SDK_DATA_ATTRIBUTES = SDK_SETTINGS;

export const SDK_QUERY_KEYS = {
  COMPONENTS: ("components": "components"),

  ENV: ("env": "env"),
  DEBUG: ("debug": "debug"),
  CACHEBUST: ("cachebust": "cachebust"),

  CLIENT_ID: ("client-id": "client-id"),
  MERCHANT_ID: ("merchant-id": "merchant-id"),

  LOCALE: ("locale": "locale"),

  CURRENCY: ("currency": "currency"),
  INTENT: ("intent": "intent"),
  COMMIT: ("commit": "commit"),
  VAULT: ("vault": "vault"),

  BUYER_COUNTRY: ("buyer-country": "buyer-country"),

  ENABLE_FUNDING: ("enable-funding": "enable-funding"),
  DISABLE_FUNDING: ("disable-funding": "disable-funding"),
  DISABLE_CARD: ("disable-card": "disable-card"),

  INTEGRATION_DATE: ("integration-date": "integration-date"),
  STAGE_HOST: ("stage-host": "stage-host"),
  STAGE_ALIAS: ("stage-alias": "stage-alias"),
  CDN_REGISTRY: ("cdn-registry": "cdn-registry"),
  VERSION: ("version": "version"),
};

export const COMPONENTS = {
  BUTTONS: ("buttons": "buttons"),
  CARD_FIELDS: ("card-fields": "card-fields"),
  HOSTED_BUTTONS: ("hosted-buttons": "hosted-buttons"),
  HOSTED_FIELDS: ("hosted-fields": "hosted-fields"),
};

export const DEBUG = {
  TRUE: (true: true),
  FALSE: (false: false),
};

export const QUERY_BOOL = {
  TRUE: ("true": "true"),
  FALSE: ("false": "false"),
};

export const UNKNOWN = ("unknown": "unknown");

export const PROTOCOL = {
  HTTP: ("http": "http"),
  HTTPS: ("https": "https"),
};

export const PAGE_TYPES = {
  HOME: ("home": "home"),
  PRODUCT: ("product": "product"),
  CART: ("cart": "cart"),
  CHECKOUT: ("checkout": "checkout"),
  PRODUCT_LISTING: ("product-listing": "product-listing"),
  SEARCH_RESULTS: ("search-results": "search-results"),
  PRODUCT_DETAILS: ("product-details": "product-details"),
  MINI_CART: ("mini-cart": "mini-cart"),
};

export const MERCHANT_ID_MAX = 10;

export const DISPLAY_ONLY_VALUES = {
  VAULTABLE: ("vaultable": "vaultable"),
};
