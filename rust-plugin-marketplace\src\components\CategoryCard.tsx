import Link from 'next/link';
import { 
  Zap, 
  Shield, 
  TestTube, 
  Globe, 
  Terminal, 
  Database, 
  Network, 
  Gamepad2,
  LucideIcon
} from 'lucide-react';
import { Category } from '@/lib/types';
import { getPluginsByCategory } from '@/lib/plugins';

interface CategoryCardProps {
  category: Category;
}

const iconMap: Record<string, LucideIcon> = {
  Zap,
  Shield,
  TestTube,
  Globe,
  Terminal,
  Database,
  Network,
  Gamepad2,
};

export default function CategoryCard({ category }: CategoryCardProps) {
  const IconComponent = iconMap[category.icon] || Zap;
  const pluginCount = getPluginsByCategory(category.id).length;

  return (
    <Link href={`/categories/${category.id}`}>
      <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200 p-6 h-full group">
        <div className="flex items-center mb-4">
          <div className="bg-rust-100 w-12 h-12 rounded-lg flex items-center justify-center mr-4 group-hover:bg-rust-200 transition-colors">
            <IconComponent className="w-6 h-6 text-rust-600" />
          </div>
          <div>
            <h3 className="font-semibold text-lg text-gray-900 group-hover:text-rust-600 transition-colors">
              {category.name}
            </h3>
            <p className="text-sm text-gray-500">
              {pluginCount} plugin{pluginCount !== 1 ? 's' : ''}
            </p>
          </div>
        </div>
        
        <p className="text-gray-600 text-sm">
          {category.description}
        </p>
      </div>
    </Link>
  );
}
