import type {
    components,
    operations,
    paths,
} from "./openapi/billing_subscriptions_v1";

export interface BillingSubscriptionsV1 {
    components: components;
    operations: operations;
    paths: paths;
}

export type CreateSubscriptionRequestBody =
    BillingSubscriptionsV1["components"]["schemas"]["subscription_request_post"];
export type ReviseSubscriptionRequestBody =
    BillingSubscriptionsV1["components"]["schemas"]["subscription_revise_request"];
export type SubscriptionResponseBody =
    BillingSubscriptionsV1["components"]["schemas"]["plan"];
