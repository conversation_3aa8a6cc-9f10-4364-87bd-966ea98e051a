import { Plugin, Category } from './types';
import pluginsData from '../data/plugins.json';
import categoriesData from '../data/categories.json';

export function getAllPlugins(): Plugin[] {
  return pluginsData as Plugin[];
}

export function getPluginById(id: string): Plugin | undefined {
  return getAllPlugins().find(plugin => plugin.id === id);
}

export function getPluginsByCategory(categoryId: string): Plugin[] {
  return getAllPlugins().filter(plugin => plugin.category === categoryId);
}

export function searchPlugins(query: string): Plugin[] {
  const lowercaseQuery = query.toLowerCase();
  return getAllPlugins().filter(plugin => 
    plugin.name.toLowerCase().includes(lowercaseQuery) ||
    plugin.description.toLowerCase().includes(lowercaseQuery) ||
    plugin.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||
    plugin.author.toLowerCase().includes(lowercaseQuery)
  );
}

export function getPluginsByTags(tags: string[]): Plugin[] {
  return getAllPlugins().filter(plugin =>
    tags.some(tag => plugin.tags.includes(tag))
  );
}

export function getFeaturedPlugins(): Plugin[] {
  // Return plugins with high ratings and downloads
  return getAllPlugins()
    .filter(plugin => plugin.rating >= 4.5 && plugin.downloads >= 500)
    .sort((a, b) => b.downloads - a.downloads)
    .slice(0, 6);
}

export function getPopularPlugins(): Plugin[] {
  return getAllPlugins()
    .sort((a, b) => b.downloads - a.downloads)
    .slice(0, 8);
}

export function getRecentPlugins(): Plugin[] {
  return getAllPlugins()
    .sort((a, b) => new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime())
    .slice(0, 8);
}

export function getAllCategories(): Category[] {
  return categoriesData as Category[];
}

export function getCategoryById(id: string): Category | undefined {
  return getAllCategories().find(category => category.id === id);
}

export function formatPrice(priceInCents: number): string {
  if (priceInCents === 0) return 'Free';
  return `$${(priceInCents / 100).toFixed(2)}`;
}

export function formatFileSize(size: string): string {
  return size;
}

export function formatDownloads(downloads: number): string {
  if (downloads >= 1000000) {
    return `${(downloads / 1000000).toFixed(1)}M`;
  } else if (downloads >= 1000) {
    return `${(downloads / 1000).toFixed(1)}K`;
  }
  return downloads.toString();
}
