import Link from 'next/link';
import { ArrowRight, Star, Download, Shield, Zap } from 'lucide-react';
import PluginCard from '@/components/PluginCard';
import CategoryCard from '@/components/CategoryCard';
import { getFeaturedPlugins, getPopularPlugins, getAllCategories } from '@/lib/plugins';

export default function HomePage() {
  const featuredPlugins = getFeaturedPlugins();
  const popularPlugins = getPopularPlugins();
  const categories = getAllCategories().slice(0, 6);

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-rust-600 to-rust-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Supercharge Your Rust Development
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-rust-100 max-w-3xl mx-auto">
              Discover premium plugins and tools that enhance your Rust projects. 
              From performance optimization to security analysis.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/plugins"
                className="bg-white text-rust-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-flex items-center justify-center"
              >
                Browse Plugins
                <ArrowRight className="ml-2 w-5 h-5" />
              </Link>
              <Link
                href="/categories"
                className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-rust-600 transition-colors inline-flex items-center justify-center"
              >
                Explore Categories
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Why Choose Our Marketplace?</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              We provide the highest quality Rust plugins with comprehensive support and documentation.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-rust-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-rust-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Secure & Tested</h3>
              <p className="text-gray-600">All plugins are thoroughly tested and security-reviewed before publication.</p>
            </div>
            <div className="text-center">
              <div className="bg-rust-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="w-8 h-8 text-rust-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">High Performance</h3>
              <p className="text-gray-600">Optimized plugins that enhance your application performance.</p>
            </div>
            <div className="text-center">
              <div className="bg-rust-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="w-8 h-8 text-rust-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Community Driven</h3>
              <p className="text-gray-600">Built by the community, for the community with continuous updates.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Plugins */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900">Featured Plugins</h2>
            <Link href="/plugins" className="text-rust-600 hover:text-rust-700 font-medium inline-flex items-center">
              View All
              <ArrowRight className="ml-1 w-4 h-4" />
            </Link>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredPlugins.map((plugin) => (
              <PluginCard key={plugin.id} plugin={plugin} />
            ))}
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Browse by Category</h2>
            <p className="text-lg text-gray-600">Find plugins organized by functionality and use case.</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories.map((category) => (
              <CategoryCard key={category.id} category={category} />
            ))}
          </div>
        </div>
      </section>

      {/* Popular Plugins */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900">Popular This Month</h2>
            <Link href="/plugins?sort=popular" className="text-rust-600 hover:text-rust-700 font-medium inline-flex items-center">
              View All
              <ArrowRight className="ml-1 w-4 h-4" />
            </Link>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {popularPlugins.slice(0, 4).map((plugin) => (
              <PluginCard key={plugin.id} plugin={plugin} compact />
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
