/*********************************************
 * Common reference to the script identifier *
 *********************************************/
export declare const SCRIPT_ID = "data-react-paypal-script-id";
export declare const SDK_SETTINGS: {
    DATA_CLIENT_TOKEN: string;
    DATA_JS_SDK_LIBRARY: string;
    DATA_LIBRARY_VALUE: string;
    DATA_NAMESPACE: string;
    DATA_SDK_INTEGRATION_SOURCE: string;
    DATA_USER_ID_TOKEN: string;
};
export declare const LOAD_SCRIPT_ERROR = "Failed to load the PayPal JS SDK script.";
/****************************
 * Braintree error messages *
 ****************************/
export declare const EMPTY_BRAINTREE_AUTHORIZATION_ERROR_MESSAGE = "Invalid authorization data. Use dataClientToken or dataUserIdToken to authorize.";
export declare const BRAINTREE_SOURCE: string;
export declare const BRAINTREE_PAYPAL_CHECKOUT_SOURCE: string;
/*********************
 * PayPal namespaces *
 *********************/
export declare const DEFAULT_PAYPAL_NAMESPACE = "paypal";
export declare const DEFAULT_BRAINTREE_NAMESPACE = "braintree";
/*****************
 * Hosted Fields *
 *****************/
export declare const HOSTED_FIELDS_CHILDREN_ERROR = "To use HostedFields you must use it with at least 3 children with types: [number, cvv, expirationDate] includes";
export declare const HOSTED_FIELDS_DUPLICATE_CHILDREN_ERROR = "Cannot use duplicate HostedFields as children";
/*******************
 * Script Provider *
 *******************/
export declare const SCRIPT_PROVIDER_REDUCER_ERROR = "usePayPalScriptReducer must be used within a PayPalScriptProvider";
/*******************
 * Card Fields *
 *******************/
export declare const CARD_ERRORS: {
    readonly INELIGIBLE_CARD_VENDOR: "INELIGIBLE_CARD_VENDOR";
    readonly INVALID_NUMBER: "INVALID_NUMBER";
    readonly INVALID_EXPIRY: "INVALID_EXPIRY";
    readonly INVALID_CVV: "INVALID_CVV";
    readonly INVALID_NAME: "INVALID_NAME";
};
export declare const CARD_FIELDS_CHILDREN_ERROR = "To use CardFields you must use it with at least the 3 required fields (PayPalNumberField, PayPalCVVField, PayPalExpiryField)";
export declare const CARD_FIELDS_DUPLICATE_CHILDREN_ERROR = "Cannot use duplicate CardFields as children";
export declare const CARD_FIELDS_CONTEXT_ERROR = "Individual CardFields must be rendered inside the PayPalCardFieldsProvider";
