'use client';

import { notFound } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import {
  Star,
  Download,
  ShoppingCart,
  Calendar,
  User,
  Tag,
  ExternalLink,
  Shield,
  FileText,
  Github
} from 'lucide-react';
import { getPluginById, formatPrice, formatDownloads } from '@/lib/plugins';
import { addToCart } from '@/lib/cart';
import { Plugin } from '@/lib/types';

interface PluginPageProps {
  params: {
    id: string;
  };
}

export default function PluginPage({ params }: PluginPageProps) {
  const plugin = getPluginById(params.id);

  if (!plugin) {
    notFound();
  }

  const handleAddToCart = () => {
    addToCart(plugin);
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2">
          {/* Plugin Header */}
          <div className="mb-8">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">{plugin.name}</h1>
                <p className="text-lg text-gray-600 mb-4">{plugin.description}</p>
                <div className="flex items-center space-x-6 text-sm text-gray-500">
                  <div className="flex items-center">
                    <User className="w-4 h-4 mr-1" />
                    <span>by {plugin.author}</span>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    <span>Updated {new Date(plugin.lastUpdated).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center">
                    <Tag className="w-4 h-4 mr-1" />
                    <span>v{plugin.version}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Stats */}
            <div className="flex items-center space-x-6 mb-6">
              <div className="flex items-center">
                <Star className="w-5 h-5 text-yellow-400 mr-1" />
                <span className="font-semibold">{plugin.rating}</span>
                <span className="text-gray-500 ml-1">({plugin.reviews.length} reviews)</span>
              </div>
              <div className="flex items-center">
                <Download className="w-5 h-5 text-gray-400 mr-1" />
                <span>{formatDownloads(plugin.downloads)} downloads</span>
              </div>
              <div className="flex items-center">
                <FileText className="w-5 h-5 text-gray-400 mr-1" />
                <span>{plugin.fileSize}</span>
              </div>
            </div>

            {/* Tags */}
            <div className="flex flex-wrap gap-2 mb-6">
              {plugin.tags.map((tag) => (
                <span
                  key={tag}
                  className="bg-rust-50 text-rust-700 px-3 py-1 rounded-full text-sm"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>

          {/* Screenshots */}
          {plugin.screenshots.length > 0 && (
            <div className="mb-8">
              <h2 className="text-xl font-semibold mb-4">Screenshots</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {plugin.screenshots.map((screenshot, index) => (
                  <div key={index} className="bg-gray-200 rounded-lg h-48 flex items-center justify-center">
                    <span className="text-gray-500">Screenshot {index + 1}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Description */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Description</h2>
            <div className="prose max-w-none">
              <p className="text-gray-700 leading-relaxed">{plugin.longDescription}</p>
            </div>
          </div>

          {/* Features */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Features</h2>
            <ul className="space-y-2">
              {plugin.features.map((feature, index) => (
                <li key={index} className="flex items-start">
                  <div className="w-2 h-2 bg-rust-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="text-gray-700">{feature}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Requirements */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Requirements</h2>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Rust Version</h3>
                  <p className="text-gray-700">{plugin.requirements.rustVersion}</p>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Dependencies</h3>
                  <div className="space-y-1">
                    {plugin.requirements.dependencies.map((dep, index) => (
                      <span key={index} className="inline-block bg-white px-2 py-1 rounded text-sm text-gray-700 mr-2">
                        {dep}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Changelog */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Changelog</h2>
            <div className="space-y-4">
              {plugin.changelog.map((entry, index) => (
                <div key={index} className="border-l-4 border-rust-200 pl-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium text-gray-900">Version {entry.version}</h3>
                    <span className="text-sm text-gray-500">{new Date(entry.date).toLocaleDateString()}</span>
                  </div>
                  <ul className="space-y-1">
                    {entry.changes.map((change, changeIndex) => (
                      <li key={changeIndex} className="text-gray-700 text-sm">• {change}</li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="sticky top-8">
            {/* Purchase Card */}
            <div className="bg-white rounded-lg shadow-lg border p-6 mb-6">
              <div className="text-center mb-6">
                <div className="text-3xl font-bold text-rust-600 mb-2">
                  {formatPrice(plugin.price)}
                </div>
                {plugin.price > 0 && (
                  <p className="text-sm text-gray-500">One-time purchase</p>
                )}
              </div>

              <div className="space-y-3">
                <button
                  onClick={handleAddToCart}
                  className="w-full bg-rust-600 text-white py-3 px-4 rounded-lg hover:bg-rust-700 transition-colors font-semibold flex items-center justify-center"
                >
                  <ShoppingCart className="w-5 h-5 mr-2" />
                  {plugin.price === 0 ? 'Download Free' : 'Add to Cart'}
                </button>
                
                {plugin.sourceCodeUrl && (
                  <a
                    href={plugin.sourceCodeUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-50 transition-colors font-medium flex items-center justify-center"
                  >
                    <Github className="w-5 h-5 mr-2" />
                    View Source
                  </a>
                )}

                <a
                  href={plugin.documentation}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-50 transition-colors font-medium flex items-center justify-center"
                >
                  <ExternalLink className="w-5 h-5 mr-2" />
                  Documentation
                </a>
              </div>
            </div>

            {/* Plugin Info */}
            <div className="bg-white rounded-lg shadow-lg border p-6">
              <h3 className="font-semibold text-gray-900 mb-4">Plugin Information</h3>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">License</span>
                  <span className="text-gray-900">{plugin.licenseType}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">File Size</span>
                  <span className="text-gray-900">{plugin.fileSize}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Downloads</span>
                  <span className="text-gray-900">{formatDownloads(plugin.downloads)}</span>
                </div>
                <div>
                  <span className="text-gray-500 block mb-2">Compatibility</span>
                  <div className="flex flex-wrap gap-1">
                    {plugin.compatibility.map((platform) => (
                      <span key={platform} className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                        {platform}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
