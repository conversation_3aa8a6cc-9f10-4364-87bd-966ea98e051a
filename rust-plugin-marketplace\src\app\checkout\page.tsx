'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { PayPalScriptProvider, PayPalButtons } from '@paypal/react-paypal-js';
import { CreditCard, Wallet, Lock, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { getCart, getCartTotal, clearCart } from '@/lib/cart';
import { formatPrice } from '@/lib/plugins';
import { createStripeCheckoutSession, createPayPalOrder, capturePayPalOrder, paymentMethods } from '@/lib/payment';
import { CartItem } from '@/lib/types';

export default function CheckoutPage() {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('stripe');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  useEffect(() => {
    const items = getCart();
    if (items.length === 0) {
      router.push('/cart');
      return;
    }
    setCartItems(items);
  }, [router]);

  const total = getCartTotal();

  const handleStripeCheckout = async () => {
    setIsLoading(true);
    setError('');

    try {
      const session = await createStripeCheckoutSession(cartItems);
      // Redirect to Stripe Checkout
      window.location.href = session.url;
    } catch (err) {
      setError('Failed to initialize payment. Please try again.');
      setIsLoading(false);
    }
  };

  const handlePayPalCreateOrder = async () => {
    try {
      const orderID = await createPayPalOrder(cartItems);
      return orderID;
    } catch (err) {
      setError('Failed to create PayPal order. Please try again.');
      throw err;
    }
  };

  const handlePayPalApprove = async (data: any) => {
    try {
      const details = await capturePayPalOrder(data.orderID);
      
      // Clear cart and redirect to success page
      clearCart();
      router.push(`/checkout/success?payment_method=paypal&order_id=${data.orderID}`);
    } catch (err) {
      setError('Failed to complete PayPal payment. Please try again.');
    }
  };

  const iconMap = {
    CreditCard,
    Wallet,
  };

  if (cartItems.length === 0) {
    return (
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Your cart is empty</h1>
          <Link href="/plugins" className="text-rust-600 hover:text-rust-700">
            Continue shopping
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <Link href="/cart" className="inline-flex items-center text-rust-600 hover:text-rust-700 mb-4">
          <ArrowLeft className="w-4 h-4 mr-1" />
          Back to Cart
        </Link>
        <h1 className="text-3xl font-bold text-gray-900">Checkout</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Payment Methods */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Payment Method</h2>
          
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}

          <div className="space-y-4 mb-6">
            {paymentMethods.map((method) => {
              const IconComponent = iconMap[method.icon as keyof typeof iconMap];
              return (
                <div
                  key={method.id}
                  className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                    selectedPaymentMethod === method.id
                      ? 'border-rust-500 bg-rust-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedPaymentMethod(method.id)}
                >
                  <div className="flex items-center">
                    <input
                      type="radio"
                      name="payment-method"
                      value={method.id}
                      checked={selectedPaymentMethod === method.id}
                      onChange={() => setSelectedPaymentMethod(method.id)}
                      className="text-rust-600 focus:ring-rust-500"
                    />
                    <div className="ml-3 flex items-center">
                      <IconComponent className="w-5 h-5 text-gray-600 mr-2" />
                      <div>
                        <p className="font-medium text-gray-900">{method.name}</p>
                        <p className="text-sm text-gray-600">{method.description}</p>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Payment Buttons */}
          <div className="space-y-4">
            {selectedPaymentMethod === 'stripe' && (
              <button
                onClick={handleStripeCheckout}
                disabled={isLoading}
                className="w-full bg-rust-600 text-white py-3 px-4 rounded-lg hover:bg-rust-700 transition-colors font-semibold flex items-center justify-center disabled:opacity-50"
              >
                {isLoading ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                ) : (
                  <CreditCard className="w-5 h-5 mr-2" />
                )}
                {isLoading ? 'Processing...' : 'Pay with Card'}
              </button>
            )}

            {selectedPaymentMethod === 'paypal' && (
              <PayPalScriptProvider
                options={{
                  clientId: process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID || 'test',
                  currency: 'USD',
                }}
              >
                <PayPalButtons
                  style={{
                    layout: 'vertical',
                    color: 'blue',
                    shape: 'rect',
                    label: 'paypal',
                  }}
                  createOrder={handlePayPalCreateOrder}
                  onApprove={handlePayPalApprove}
                  onError={(err) => {
                    console.error('PayPal error:', err);
                    setError('PayPal payment failed. Please try again.');
                  }}
                />
              </PayPalScriptProvider>
            )}
          </div>

          {/* Security Notice */}
          <div className="mt-6 flex items-center text-sm text-gray-600">
            <Lock className="w-4 h-4 mr-2" />
            <span>Your payment information is secure and encrypted</span>
          </div>
        </div>

        {/* Order Summary */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Order Summary</h2>
          
          <div className="bg-gray-50 rounded-lg p-6">
            <div className="space-y-4 mb-6">
              {cartItems.map((item) => (
                <div key={item.plugin.id} className="flex justify-between items-start">
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">{item.plugin.name}</h3>
                    <p className="text-sm text-gray-600">by {item.plugin.author}</p>
                    {item.quantity > 1 && (
                      <p className="text-sm text-gray-600">Quantity: {item.quantity}</p>
                    )}
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-gray-900">
                      {formatPrice(item.plugin.price * item.quantity)}
                    </p>
                    {item.quantity > 1 && (
                      <p className="text-sm text-gray-600">
                        {formatPrice(item.plugin.price)} each
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>

            <div className="border-t border-gray-200 pt-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-gray-600">Subtotal</span>
                <span className="font-medium">{formatPrice(total)}</span>
              </div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-gray-600">Tax</span>
                <span className="font-medium">$0.00</span>
              </div>
              <div className="flex justify-between items-center text-lg font-semibold">
                <span>Total</span>
                <span className="text-rust-600">{formatPrice(total)}</span>
              </div>
            </div>

            <div className="mt-6 text-xs text-gray-500 space-y-1">
              <p>• Instant download after payment</p>
              <p>• 30-day money-back guarantee</p>
              <p>• Lifetime updates included</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
