export interface Plugin {
  id: string;
  name: string;
  description: string;
  longDescription: string;
  version: string;
  author: string;
  price: number; // in cents
  category: string;
  tags: string[];
  downloadUrl: string;
  imageUrl: string;
  screenshots: string[];
  features: string[];
  requirements: {
    rustVersion: string;
    dependencies: string[];
  };
  compatibility: string[];
  lastUpdated: string;
  downloads: number;
  rating: number;
  reviews: Review[];
  changelog: ChangelogEntry[];
  documentation: string;
  sourceCodeUrl?: string;
  licenseType: string;
  fileSize: string;
}

export interface Review {
  id: string;
  userId: string;
  userName: string;
  rating: number;
  comment: string;
  date: string;
}

export interface ChangelogEntry {
  version: string;
  date: string;
  changes: string[];
}

export interface Category {
  id: string;
  name: string;
  description: string;
  icon: string;
}

export interface CartItem {
  plugin: Plugin;
  quantity: number;
}

export interface User {
  id: string;
  email: string;
  name: string;
  purchasedPlugins: string[];
}
