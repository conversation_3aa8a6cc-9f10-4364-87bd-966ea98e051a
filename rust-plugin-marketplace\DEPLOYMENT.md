# Rust Game Plugin Marketplace - Deployment Guide

This guide will help you deploy the Rust Game Plugin Marketplace on your Ubuntu server with your domain.

## Prerequisites

- Ubuntu server (18.04 or later)
- Node.js 18+ and npm
- Domain name pointed to your server
- SSL certificate (Let's Encrypt recommended)

## Step 1: Server Setup

### Update your system
```bash
sudo apt update && sudo apt upgrade -y
```

### Install Node.js and npm
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### Install PM2 for process management
```bash
sudo npm install -g pm2
```

### Install Nginx for reverse proxy
```bash
sudo apt install nginx -y
```

## Step 2: Deploy the Application

### Clone or upload your project
```bash
# If using git
git clone <your-repository-url> /var/www/rust-plugin-marketplace
cd /var/www/rust-plugin-marketplace

# Or upload your project files to /var/www/rust-plugin-marketplace
```

### Install dependencies and build
```bash
npm install
npm run build
```

### Set up environment variables
```bash
# Create environment file
sudo nano .env.local
```

Add the following environment variables:
```env
# Production settings
NODE_ENV=production
NEXT_PUBLIC_SITE_URL=https://yourdomain.com

# Payment settings (get these from your payment providers)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_key
STRIPE_SECRET_KEY=sk_live_your_stripe_secret

NEXT_PUBLIC_PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_secret
```

### Start the application with PM2
```bash
# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'rust-plugin-marketplace',
    script: 'npm',
    args: 'start',
    cwd: '/var/www/rust-plugin-marketplace',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
}
EOF

# Start the application
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

## Step 3: Configure Nginx

### Create Nginx configuration
```bash
sudo nano /etc/nginx/sites-available/rust-plugin-marketplace
```

Add the following configuration (replace `yourdomain.com` with your actual domain):

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL Configuration (update paths to your certificates)
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    
    # SSL Security
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Static files caching
    location /_next/static {
        alias /var/www/rust-plugin-marketplace/.next/static;
        expires 365d;
        access_log off;
    }

    # Favicon and robots.txt
    location = /favicon.ico {
        log_not_found off;
        access_log off;
    }

    location = /robots.txt {
        log_not_found off;
        access_log off;
        allow all;
    }
}
```

### Enable the site
```bash
sudo ln -s /etc/nginx/sites-available/rust-plugin-marketplace /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## Step 4: SSL Certificate with Let's Encrypt

### Install Certbot
```bash
sudo apt install certbot python3-certbot-nginx -y
```

### Get SSL certificate
```bash
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
```

### Set up auto-renewal
```bash
sudo crontab -e
# Add this line:
0 12 * * * /usr/bin/certbot renew --quiet
```

## Step 5: Firewall Configuration

```bash
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

## Step 6: Payment Provider Setup

### Stripe Setup
1. Create a Stripe account at https://stripe.com
2. Get your publishable and secret keys from the dashboard
3. Add them to your `.env.local` file
4. Set up webhooks for payment confirmation

### PayPal Setup
1. Create a PayPal Developer account at https://developer.paypal.com
2. Create a new app and get your client ID and secret
3. Add them to your `.env.local` file
4. Configure webhooks for payment notifications

## Step 7: Monitoring and Maintenance

### Monitor the application
```bash
# Check PM2 status
pm2 status

# View logs
pm2 logs rust-plugin-marketplace

# Restart if needed
pm2 restart rust-plugin-marketplace
```

### Regular maintenance
```bash
# Update the application
cd /var/www/rust-plugin-marketplace
git pull  # if using git
npm install
npm run build
pm2 restart rust-plugin-marketplace

# Check Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

## Troubleshooting

### Common Issues

1. **Port 3000 already in use**
   ```bash
   sudo lsof -i :3000
   sudo kill -9 <PID>
   ```

2. **Permission issues**
   ```bash
   sudo chown -R www-data:www-data /var/www/rust-plugin-marketplace
   sudo chmod -R 755 /var/www/rust-plugin-marketplace
   ```

3. **Nginx configuration test fails**
   ```bash
   sudo nginx -t
   # Fix any syntax errors shown
   ```

4. **SSL certificate issues**
   ```bash
   sudo certbot certificates
   sudo certbot renew --dry-run
   ```

## Security Considerations

1. Keep your server updated
2. Use strong passwords and SSH keys
3. Regularly backup your data
4. Monitor access logs
5. Keep payment provider credentials secure
6. Use environment variables for sensitive data

## Performance Optimization

1. Enable Nginx caching for static files
2. Use a CDN for global content delivery
3. Monitor server resources with tools like htop
4. Consider using Redis for session storage
5. Implement database optimization if using a database

Your Rust Game Plugin Marketplace should now be live at your domain!
