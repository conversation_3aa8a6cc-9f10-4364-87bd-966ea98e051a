'use client';

import { Plugin, CartItem } from './types';

const CART_STORAGE_KEY = 'rust-plugins-cart';

export function getCart(): CartItem[] {
  if (typeof window === 'undefined') return [];
  
  try {
    const cartData = localStorage.getItem(CART_STORAGE_KEY);
    return cartData ? JSON.parse(cartData) : [];
  } catch {
    return [];
  }
}

export function addToCart(plugin: Plugin): void {
  const cart = getCart();
  const existingItem = cart.find(item => item.plugin.id === plugin.id);
  
  if (existingItem) {
    existingItem.quantity += 1;
  } else {
    cart.push({ plugin, quantity: 1 });
  }
  
  saveCart(cart);
}

export function removeFromCart(pluginId: string): void {
  const cart = getCart().filter(item => item.plugin.id !== pluginId);
  saveCart(cart);
}

export function updateCartItemQuantity(pluginId: string, quantity: number): void {
  const cart = getCart();
  const item = cart.find(item => item.plugin.id === pluginId);
  
  if (item) {
    if (quantity <= 0) {
      removeFromCart(pluginId);
    } else {
      item.quantity = quantity;
      saveCart(cart);
    }
  }
}

export function clearCart(): void {
  saveCart([]);
}

export function getCartTotal(): number {
  return getCart().reduce((total, item) => total + (item.plugin.price * item.quantity), 0);
}

export function getCartItemCount(): number {
  return getCart().reduce((count, item) => count + item.quantity, 0);
}

function saveCart(cart: CartItem[]): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(cart));
    // Dispatch custom event for cart updates
    window.dispatchEvent(new CustomEvent('cartUpdated'));
  }
}
