/* @flow */

import { COUNTRY } from "./locale";
import { CURRENCY, INTENT, COMMIT, VAULT } from "./order";
import { COMPONENTS, DEBUG } from "./params";

export const DEFAULT_COUNTRY = COUNTRY.US;
export const DEFAULT_CURRENCY = CURRENCY.USD;
export const DEFAULT_INTENT = INTENT.CAPTURE;
export const DEFAULT_COMMIT = COMMIT.TRUE;
export const DEFAULT_SALE_COMMIT = COMMIT.TRUE;
export const DEFAULT_NONSALE_COMMIT = COMMIT.TRUE;
export const DEFAULT_VAULT = VAULT.FALSE;
export const DEFAULT_COMPONENTS = COMPONENTS.BUTTONS;
export const DEFAULT_DEBUG = DEBUG.FALSE;
