"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/plugins/page",{

/***/ "(app-pages-browser)/./src/components/PluginCard.tsx":
/*!***************************************!*\
  !*** ./src/components/PluginCard.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PluginCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Download_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Download_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _lib_plugins__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/plugins */ \"(app-pages-browser)/./src/lib/plugins.ts\");\n/* harmony import */ var _lib_cart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/cart */ \"(app-pages-browser)/./src/lib/cart.ts\");\n\n\n\n\n\n\nfunction PluginCard(param) {\n    let { plugin, compact = false } = param;\n    const cardClasses = compact ? \"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200\" : \"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 h-full\";\n    const handleAddToCart = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        (0,_lib_cart__WEBPACK_IMPORTED_MODULE_4__.addToCart)(plugin);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: cardClasses,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/plugins/\".concat(plugin.id),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-48 bg-gray-200 rounded-t-lg flex items-center justify-center\",\n                            children: plugin.imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                src: plugin.imageUrl,\n                                alt: plugin.name,\n                                width: 400,\n                                height: 200,\n                                className: \"w-full h-full object-cover rounded-t-lg\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-rust-100 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-rust-600 font-bold text-xl\",\n                                            children: plugin.name.charAt(0)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"No Image\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        plugin.price === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold\",\n                            children: \"FREE\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/plugins/\".concat(plugin.id),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold text-lg text-gray-900 mb-2 hover:text-rust-600 transition-colors line-clamp-1\",\n                            children: plugin.name\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-sm mb-3 line-clamp-2\",\n                        children: plugin.description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    \"by \",\n                                    plugin.author\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded\",\n                                children: [\n                                    \"v\",\n                                    plugin.version\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4 text-yellow-400 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: plugin.rating\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-4 h-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: (0,_lib_plugins__WEBPACK_IMPORTED_MODULE_3__.formatDownloads)(plugin.downloads)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl font-bold text-rust-600\",\n                                children: (0,_lib_plugins__WEBPACK_IMPORTED_MODULE_3__.formatPrice)(plugin.price)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"bg-rust-600 text-white px-4 py-2 rounded-lg hover:bg-rust-700 transition-colors flex items-center text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, this),\n                                    plugin.price === 0 ? \"Download\" : \"Add to Cart\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    !compact && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 flex flex-wrap gap-1\",\n                        children: [\n                            plugin.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs bg-rust-50 text-rust-700 px-2 py-1 rounded\",\n                                    children: tag\n                                }, tag, false, {\n                                    fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 15\n                                }, this)),\n                            plugin.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500\",\n                                children: [\n                                    \"+\",\n                                    plugin.tags.length - 3,\n                                    \" more\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\WebSiteStuff\\\\rust-plugin-marketplace\\\\src\\\\components\\\\PluginCard.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n_c = PluginCard;\nvar _c;\n$RefreshReg$(_c, \"PluginCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PluginCard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/cart.ts":
/*!*************************!*\
  !*** ./src/lib/cart.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToCart: function() { return /* binding */ addToCart; },\n/* harmony export */   clearCart: function() { return /* binding */ clearCart; },\n/* harmony export */   getCart: function() { return /* binding */ getCart; },\n/* harmony export */   getCartItemCount: function() { return /* binding */ getCartItemCount; },\n/* harmony export */   getCartTotal: function() { return /* binding */ getCartTotal; },\n/* harmony export */   removeFromCart: function() { return /* binding */ removeFromCart; },\n/* harmony export */   updateCartItemQuantity: function() { return /* binding */ updateCartItemQuantity; }\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ getCart,addToCart,removeFromCart,updateCartItemQuantity,clearCart,getCartTotal,getCartItemCount auto */ const CART_STORAGE_KEY = \"rust-plugins-cart\";\nfunction getCart() {\n    if (false) {}\n    try {\n        const cartData = localStorage.getItem(CART_STORAGE_KEY);\n        return cartData ? JSON.parse(cartData) : [];\n    } catch (e) {\n        return [];\n    }\n}\nfunction addToCart(plugin) {\n    const cart = getCart();\n    const existingItem = cart.find((item)=>item.plugin.id === plugin.id);\n    if (existingItem) {\n        existingItem.quantity += 1;\n    } else {\n        cart.push({\n            plugin,\n            quantity: 1\n        });\n    }\n    saveCart(cart);\n}\nfunction removeFromCart(pluginId) {\n    const cart = getCart().filter((item)=>item.plugin.id !== pluginId);\n    saveCart(cart);\n}\nfunction updateCartItemQuantity(pluginId, quantity) {\n    const cart = getCart();\n    const item = cart.find((item)=>item.plugin.id === pluginId);\n    if (item) {\n        if (quantity <= 0) {\n            removeFromCart(pluginId);\n        } else {\n            item.quantity = quantity;\n            saveCart(cart);\n        }\n    }\n}\nfunction clearCart() {\n    saveCart([]);\n}\nfunction getCartTotal() {\n    return getCart().reduce((total, item)=>total + item.plugin.price * item.quantity, 0);\n}\nfunction getCartItemCount() {\n    return getCart().reduce((count, item)=>count + item.quantity, 0);\n}\nfunction saveCart(cart) {\n    if (true) {\n        localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(cart));\n        // Dispatch custom event for cart updates\n        window.dispatchEvent(new CustomEvent(\"cartUpdated\"));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/cart.ts\n"));

/***/ })

});