export var FPTI_KEY = {
  BUTTON_LAYOUT: "button_layout",
  BUTTON_MESSAGE_AMOUNT: "button_message_amount",
  BUTTON_MESSAGE_CREDIT_PRODUCT_IDENTIFIER: "button_message_credit_product_identifier",
  BUTTON_MESSAGE_COLOR: "button_message_color",
  BUTTON_MESSAGE_CURRENCY: "button_message_currency",
  BUTTON_MESSAGE_ALIGN: "button_message_align",
  BUTTON_MESSAGE_POSITION: "button_message_position",
  BUTTON_MESSAGE_OFFER_COUNTRY: "button_message_offer_country",
  BUTTON_MESSAGE_OFFER_TYPE: "button_message_offer_type",
  BUTTON_MESSAGE_TYPE: "button_message_type",
  BUTTON_SESSION_UID: "button_session_id",
  BUTTON_SOURCE: "button_source",
  BUTTON_TYPE: "button_type",
  BUTTON_VERSION: "button_version",
  BUYER_COUNTRY: "buyer_cntry",
  CHECKOUT_APP: "checkout_app",
  CHOSEN_FI_TYPE: "chosen_fi_type",
  CHOSEN_FI_ID: "chosen_fi_id",
  CHOSEN_FUNDING: "selected_payment_method",
  CLIENT_ID: "client_id",
  CONTEXT_CORRID: "context_correlation_id",
  CONTEXT_ID: "context_id",
  CONTEXT_TYPE: "context_type",
  CPL_CHUNK_METRICS: "cpl_chunk_metrics",
  CPL_COMP_METRICS: "cpl_comp_metrics",
  CPL_QUERY_METRICS: "cpl_query_metrics",
  DATA_SOURCE: "serverside_data_source",
  DISABLE_CARD: "disable_card",
  DISABLE_FUNDING: "disable_funding",
  ERROR_CODE: "ext_error_code",
  ERROR_DESC: "ext_error_desc",
  EVENT_NAME: "event_name",
  EXPERIMENT_EXPERIENCE: "experimentation_experience",
  EXPERIMENT_NAME: "pxp_exp_id",
  EXPERIMENT_TREATMENT: "experimentation_treatment",
  FEED: "feed_name",
  FI_ID: "fi_id",
  FI_LIST: "fi_list",
  FIELDS_COMPONENT_SESSION_ID: "fields_component_session_id",
  FLOW: "flow",
  FUNDING_COUNT: "eligible_payment_count",
  FUNDING_LIST: "eligible_payment_methods",
  HOSTED_BUTTON_ID: "hosted_button_id",
  INTEGRATION_IDENTIFIER: "integration_identifier",
  IS_VAULT: "is_vault",
  JS_SDK_LIBRARY: "js_sdk_library",
  LOCALE: "locale",
  MERCHANT_DOMAIN: "merchant_domain",
  MOBILE_APP_VERSION: "mobile_app_version",
  MOBILE_BUNDLE_IDENTIFIER: "mapv",
  OPTION_SELECTED: "optsel",
  PAGE: "page_name",
  PAGE_LOAD_TIME: "page_load_time",
  PAGE_TYPE: "pp_placement",
  PARTNER_ATTRIBUTION_ID: "bn_code",
  PAY_ID: "pay_id",
  PAY_NOW: "pay_now",
  PAYMENT_FLOW: "payment_flow",
  POTENTIAL_PAYMENT_METHODS: "potential_payment_methods",
  PRODUCT: "product",
  RECOMMENDED_PAYMENT: "recommended_payment",
  REFERER: "referer_url",
  REFERRER_DOMAIN: ("referrer_domain", "referrer_domain"),
  RESPONSE_DURATION: "response_duration",
  SDK_CACHE: "sdk_cache",
  SDK_ENVIRONMENT: "sdk_environment",
  SDK_INTEGRATION_SOURCE: "sdk_integration_source",
  SDK_LOAD_TIME: "sdk_load_time",
  SDK_NAME: "sdk_name",
  SDK_VERSION: "sdk_version",
  SELECTED_FI: "merchant_selected_funding_source",
  SELLER_ID: "seller_id",
  SESSION_UID: "page_session_id",
  SMART_WALLET_INSTRUMENT_TYPES: "smart_wallet_instrument_types",
  SPACE_KEY: "space_key",
  STATE: "state_name",
  STICKINESS_ID: "stickiness_id",
  TIMESTAMP: "t",
  TOKEN: "token",
  TRANSITION: "transition_name",
  TRANSITION_TIME: "transition_time",
  TREATMENT_NAME: "pxp_trtmnt_id",
  USER_ACTION: "user_action",
  USER_AGENT: "user_agent",
  USER_IDENTITY_METHOD: "user_identity_method",
  VERSION: "checkoutjs_version",
  AVAILABLE_PAYMENT_NETWORKS: "available_payment_networks",
  SELECTED_CARD_TYPE: "selected_card_type",
  CURRENCY: "currency",
  AMOUNT: "amount"
};
export var FPTI_USER_ACTION = {
  COMMIT: "commit",
  CONTINUE: "continue"
};
export var FPTI_DATA_SOURCE = {
  PAYMENTS_SDK: "checkout"
};
export var FPTI_FEED = {
  PAYMENTS_SDK: "payments_sdk"
};
export var FPTI_SDK_NAME = {
  PAYMENTS_SDK: "payments_sdk"
};