[{"id": "advanced-base-builder", "name": "Advanced Base Builder", "description": "Enhanced building tools and automation for Rust servers", "longDescription": "This plugin provides advanced building tools including auto-upgrade systems, building templates, and smart resource management. Perfect for server admins who want to enhance the building experience for their players with automated systems and quality-of-life improvements.", "version": "2.1.0", "author": "RustMods Studio", "price": 2999, "category": "building", "tags": ["building", "automation", "templates", "base"], "downloadUrl": "/downloads/advanced-base-builder-2.1.0.zip", "imageUrl": "/images/plugins/base-builder.jpg", "screenshots": ["/images/screenshots/base-builder-1.jpg", "/images/screenshots/base-builder-2.jpg"], "features": ["Auto-upgrade building materials", "Building templates and blueprints", "Smart resource allocation", "Decay protection systems", "Advanced building permissions"], "requirements": {"rustVersion": "Latest", "dependencies": ["Oxide", "uMod"]}, "compatibility": ["Windows Server", "Linux Server"], "lastUpdated": "2024-01-15", "downloads": 1250, "rating": 4.8, "reviews": [], "changelog": [{"version": "2.1.0", "date": "2024-01-15", "changes": ["Added auto-upgrade for stone to metal", "Improved building template system", "Fixed compatibility issues with latest Rust update"]}], "documentation": "https://docs.example.com/advanced-base-builder", "sourceCodeUrl": "https://github.com/example/advanced-base-builder", "licenseType": "MIT", "fileSize": "2.3 MB"}, {"id": "anti-cheat-pro", "name": "Anti-Cheat Pro", "description": "Advanced anti-cheat and player monitoring system for Rust servers", "longDescription": "A comprehensive anti-cheat solution that monitors player behavior, detects suspicious activities, and automatically takes action against cheaters. Includes advanced detection algorithms, player reporting systems, and detailed logging for server administrators.", "version": "1.5.2", "author": "RustSecurity Team", "price": 3999, "category": "admin", "tags": ["anticheat", "security", "monitoring", "admin"], "downloadUrl": "/downloads/anti-cheat-pro-1.5.2.zip", "imageUrl": "/images/plugins/anti-cheat.jpg", "screenshots": ["/images/screenshots/anti-cheat-1.jpg", "/images/screenshots/anti-cheat-2.jpg"], "features": ["Real-time player monitoring", "Automatic cheat detection", "Player behavior analysis", "Detailed admin reports", "Customizable punishment system"], "requirements": {"rustVersion": "Latest", "dependencies": ["Oxide", "uMod"]}, "compatibility": ["Windows Server", "Linux Server"], "lastUpdated": "2024-01-10", "downloads": 890, "rating": 4.9, "reviews": [], "changelog": [{"version": "1.5.2", "date": "2024-01-10", "changes": ["Enhanced aimbot detection algorithms", "Added support for new cheat patterns", "Improved performance for high-population servers"]}], "documentation": "https://docs.example.com/anti-cheat-pro", "licenseType": "Commercial", "fileSize": "1.8 MB"}]