import type { FC } from "react";
import type { PayPalMessagesComponentOptions } from "@paypal/paypal-js";
export interface PayPalMessagesComponentProps extends PayPalMessagesComponentOptions {
    forceReRender?: unknown[];
    className?: string;
}
/**
This `<PayPalMessages />` messages component renders a credit messaging on upstream merchant sites.
It relies on the `<PayPalScriptProvider />` parent component for managing state related to loading the JS SDK script.
*/
export declare const PayPalMessages: FC<PayPalMessagesComponentProps>;
