{"version": 3, "sources": ["webpack://ppsdkconstants/webpack/universalModuleDefinition", "webpack://ppsdkconstants/webpack/bootstrap", "webpack://ppsdkconstants/./src/funding.js", "webpack://ppsdkconstants/./src/apm.js", "webpack://ppsdkconstants/./src/locale.js", "webpack://ppsdkconstants/./src/order.js", "webpack://ppsdkconstants/./src/params.js", "webpack://ppsdkconstants/./src/defaults.js", "webpack://ppsdkconstants/./src/env.js", "webpack://ppsdkconstants/./src/error.js", "webpack://ppsdkconstants/./src/fpti.js", "webpack://ppsdkconstants/./src/observability.js", "webpack://ppsdkconstants/./src/platform.js", "webpack://ppsdkconstants/./src/types.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "this", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "hasOwnProperty", "p", "s", "FUNDING", "PAYPAL", "VENMO", "APPLEPAY", "ITAU", "CREDIT", "PAYLATER", "CARD", "IDEAL", "SEPA", "BANCONTACT", "GIROPAY", "SOFORT", "EPS", "MYBANK", "P24", "PAYU", "BLIK", "TRUSTLY", "OXXO", "BOLETO", "BOLETOBANCARIO", "WECHATPAY", "MERCADOPAGO", "MULTIBANCO", "SATISPAY", "PAIDY", "ZIMPLER", "MAXIMA", "FUNDING_BRAND_LABEL", "VISA", "MASTERCARD", "AMEX", "DISCOVER", "HIPER", "ELO", "JCB", "CUP", "DINERS", "MAESTRO", "EFTPOS", "CB_NATIONALE", "WALLET_INSTRUMENT", "BALANCE", "BANK", "FUNDING_PRODUCTS", "PAY_IN_3", "PAY_IN_4", "APM_LIST", "COUNTRY", "AD", "AE", "AG", "AI", "AL", "AM", "AN", "AO", "AR", "AT", "AU", "AW", "AZ", "BA", "BB", "BE", "BF", "BG", "BH", "BI", "BJ", "BM", "BN", "BO", "BR", "BS", "BT", "BW", "BY", "BZ", "CA", "CD", "CG", "CH", "CI", "CK", "CL", "CM", "CN", "CO", "CR", "CV", "CY", "CZ", "DE", "DJ", "DK", "DM", "DO", "DZ", "EC", "EE", "EG", "ER", "ES", "ET", "FI", "FJ", "FK", "FM", "FO", "FR", "GA", "GB", "GD", "GE", "GF", "GI", "GL", "GM", "GN", "GP", "GR", "GT", "GW", "GY", "HK", "HN", "HR", "HU", "ID", "IE", "IL", "IN", "IS", "IT", "JM", "JO", "JP", "KE", "KG", "KH", "KI", "KM", "KN", "KR", "KW", "KY", "KZ", "LA", "LC", "LI", "LK", "LS", "LT", "LU", "LV", "MA", "MC", "MD", "ME", "MG", "MH", "MK", "ML", "MN", "MQ", "MR", "MS", "MT", "MU", "MV", "MW", "MX", "MY", "MZ", "NA", "NC", "NE", "NF", "NG", "NI", "NL", "NO", "NP", "NR", "NU", "NZ", "OM", "PA", "PE", "PF", "PG", "PH", "PL", "PM", "PN", "PT", "PW", "PY", "QA", "RE", "RO", "RS", "RU", "RW", "SA", "SB", "SC", "SE", "SG", "SH", "SI", "SJ", "SK", "SL", "SM", "SN", "SO", "SR", "ST", "SV", "SZ", "TC", "TD", "TG", "TH", "TJ", "TM", "TN", "TO", "TR", "TT", "TV", "TW", "TZ", "UA", "UG", "US", "UY", "VA", "VC", "VE", "VG", "VN", "VU", "WF", "WS", "YE", "YT", "ZA", "ZM", "ZW", "LANG", "CS", "DA", "EL", "EN", "HE", "JA", "KO", "SQ", "TL", "VI", "ZH", "ZH_HANT", "COUNTRY_LANGS", "INTENT", "CAPTURE", "AUTHORIZE", "ORDER", "TOKENIZE", "SUBSCRIPTION", "COMMIT", "TRUE", "FALSE", "VAULT", "CURRENCY", "AED", "AFN", "ALL", "AMD", "ANG", "ARS", "AOA", "AUD", "AWG", "AZN", "BAM", "BBD", "BDT", "BGN", "BHD", "BIF", "BMD", "BND", "BOB", "BRL", "BSD", "BTN", "BWP", "BZD", "CAD", "CDF", "CHF", "CLP", "COP", "CRC", "CVE", "CZK", "DJF", "DKK", "DOP", "DZD", "EGP", "ETB", "ERN", "EUR", "FJD", "FKP", "GBP", "GEL", "GHS", "GIP", "GMD", "GNF", "GTQ", "GYD", "HKD", "HNL", "HRK", "HTG", "HUF", "IDR", "ILS", "INR", "ISK", "JMD", "JOD", "JPY", "KGS", "KES", "KHR", "KMF", "KRW", "KWD", "KYD", "KZT", "LAK", "LKR", "LRD", "LSL", "MAD", "MDL", "MGA", "MKD", "MNT", "MOP", "MRO", "MRU", "MUR", "MVR", "MWK", "MXN", "MYR", "MZN", "NAD", "NGN", "NIO", "NOK", "NPR", "NZD", "OMR", "PAB", "PEN", "PGK", "PHP", "PKR", "PLN", "PYG", "QAR", "RON", "RSD", "RUB", "RWF", "SAR", "SBD", "SCR", "SEK", "SGD", "SHP", "SLE", "SLL", "SOS", "SRD", "STN", "SVC", "SZL", "THB", "TMT", "TJS", "TND", "TOP", "TTD", "TWD", "TZS", "UAH", "UGX", "USD", "UYU", "UZS", "VES", "VND", "VUV", "WST", "XAF", "XCD", "XOF", "XPF", "YER", "ZAR", "ZMW", "SDK_PATH", "WEB_SDK_BRIDGE_PATH", "SDK_SETTINGS", "AMOUNT", "API_STAGE_HOST", "CLIENT_METADATA_ID", "CLIENT_TOKEN", "CSP_NONCE", "ENABLE_3DS", "JS_SDK_LIBRARY", "MERCHANT_ID", "NAMESPACE", "PAGE_TYPE", "PARTNER_ATTRIBUTION_ID", "POPUPS_DISABLED", "SDK_INTEGRATION_SOURCE", "SDK_TOKEN", "SHOPPER_SESSION_ID", "STAGE_HOST", "USER_EXPERIENCE_FLOW", "USER_ID_TOKEN", "SDK_DATA_ATTRIBUTES", "SDK_QUERY_KEYS", "COMPONENTS", "ENV", "DEBUG", "CACHEBUST", "CLIENT_ID", "LOCALE", "BUYER_COUNTRY", "ENABLE_FUNDING", "DISABLE_FUNDING", "DISABLE_CARD", "INTEGRATION_DATE", "STAGE_ALIAS", "CDN_REGISTRY", "VERSION", "BUTTONS", "CARD_FIELDS", "HOSTED_BUTTONS", "HOSTED_FIELDS", "QUERY_BOOL", "UNKNOWN", "PROTOCOL", "HTTP", "HTTPS", "PAGE_TYPES", "HOME", "PRODUCT", "CART", "CHECKOUT", "PRODUCT_LISTING", "SEARCH_RESULTS", "PRODUCT_DETAILS", "MINI_CART", "MERCHANT_ID_MAX", "DISPLAY_ONLY_VALUES", "VAULTABLE", "DEFAULT_COUNTRY", "DEFAULT_CURRENCY", "DEFAULT_INTENT", "DEFAULT_COMMIT", "DEFAULT_SALE_COMMIT", "DEFAULT_NONSALE_COMMIT", "DEFAULT_VAULT", "DEFAULT_COMPONENTS", "DEFAULT_DEBUG", "LOCAL", "STAGE", "SANDBOX", "PRODUCTION", "TEST", "MOBILE_ENV", "ANDROID", "IOS", "ERROR_CODE", "VALIDATION_ERROR", "FPTI_KEY", "BUTTON_LAYOUT", "BUTTON_MESSAGE_AMOUNT", "BUTTON_MESSAGE_CREDIT_PRODUCT_IDENTIFIER", "BUTTON_MESSAGE_COLOR", "BUTTON_MESSAGE_CURRENCY", "BUTTON_MESSAGE_ALIGN", "BUTTON_MESSAGE_POSITION", "BUTTON_MESSAGE_OFFER_COUNTRY", "BUTTON_MESSAGE_OFFER_TYPE", "BUTTON_MESSAGE_TYPE", "BUTTON_SESSION_UID", "BUTTON_SOURCE", "BUTTON_TYPE", "BUTTON_VERSION", "CHECKOUT_APP", "CHOSEN_FI_TYPE", "CHOSEN_FI_ID", "CHOSEN_FUNDING", "CONTEXT_CORRID", "CONTEXT_ID", "CONTEXT_TYPE", "CPL_CHUNK_METRICS", "CPL_COMP_METRICS", "CPL_QUERY_METRICS", "DATA_SOURCE", "ERROR_DESC", "EVENT_NAME", "EXPERIMENT_EXPERIENCE", "EXPERIMENT_NAME", "EXPERIMENT_TREATMENT", "FEED", "FI_ID", "FI_LIST", "FIELDS_COMPONENT_SESSION_ID", "FLOW", "FUNDING_COUNT", "FUNDING_LIST", "HOSTED_BUTTON_ID", "INTEGRATION_IDENTIFIER", "IS_VAULT", "MERCHANT_DOMAIN", "MOBILE_APP_VERSION", "MOBILE_BUNDLE_IDENTIFIER", "OPTION_SELECTED", "PAGE", "PAGE_LOAD_TIME", "PAY_ID", "PAY_NOW", "PAYMENT_FLOW", "POTENTIAL_PAYMENT_METHODS", "RECOMMENDED_PAYMENT", "REFERER", "REFERRER_DOMAIN", "RESPONSE_DURATION", "SDK_CACHE", "SDK_ENVIRONMENT", "SDK_LOAD_TIME", "SDK_NAME", "SDK_VERSION", "SELECTED_FI", "SELLER_ID", "SESSION_UID", "SMART_WALLET_INSTRUMENT_TYPES", "SPACE_KEY", "STATE", "STICKINESS_ID", "TIMESTAMP", "TOKEN", "TRANSITION", "TRANSITION_TIME", "TREATMENT_NAME", "USER_ACTION", "USER_AGENT", "USER_IDENTITY_METHOD", "AVAILABLE_PAYMENT_NETWORKS", "SELECTED_CARD_TYPE", "FPTI_USER_ACTION", "CONTINUE", "FPTI_DATA_SOURCE", "PAYMENTS_SDK", "FPTI_FEED", "FPTI_SDK_NAME", "BASE_SDK_METRIC_NAMESPACE", "payPalWebV5Dimensions", "sdk_platform", "major_version", "PLATFORM", "DESKTOP", "MOBILE", "TYPES"], "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,iBAAkB,GAAIH,GACH,iBAAZC,QACdA,QAAwB,eAAID,IAE5BD,EAAqB,eAAIC,IAR3B,CASoB,oBAATK,KAAuBA,KAAOC,MAAO,WAChD,O,YCTE,IAAIC,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUR,QAGnC,IAAIC,EAASK,EAAiBE,GAAY,CACzCC,EAAGD,EACHE,GAAG,EACHV,QAAS,IAUV,OANAW,EAAQH,GAAUI,KAAKX,EAAOD,QAASC,EAAQA,EAAOD,QAASO,GAG/DN,EAAOS,GAAI,EAGJT,EAAOD,QA0Df,OArDAO,EAAoBM,EAAIF,EAGxBJ,EAAoBO,EAAIR,EAGxBC,EAAoBQ,EAAI,SAASf,EAASgB,EAAMC,GAC3CV,EAAoBW,EAAElB,EAASgB,IAClCG,OAAOC,eAAepB,EAASgB,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEV,EAAoBgB,EAAI,SAASvB,GACX,oBAAXwB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAepB,EAASwB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAepB,EAAS,aAAc,CAAE0B,OAAO,KAQvDnB,EAAoBoB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQnB,EAAoBmB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFAxB,EAAoBgB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOnB,EAAoBQ,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRvB,EAAoB2B,EAAI,SAASjC,GAChC,IAAIgB,EAAShB,GAAUA,EAAO4B,WAC7B,WAAwB,OAAO5B,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAM,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASiB,EAAQC,GAAY,MAAOjB,GAAiBkB,eAAezB,KAAKuB,EAAQC,IAGzG7B,EAAoB+B,EAAI,GAIjB/B,EAAoBA,EAAoBgC,EAAI,G,ymEChF9C,IAAMC,EAAU,CACrBC,OAAS,SACTC,MAAQ,QACRC,SAAW,WACXC,KAAO,OACPC,OAAS,SACTC,SAAW,WACXC,KAAO,OACPC,MAAQ,QACRC,KAAO,OACPC,WAAa,aACbC,QAAU,UACVC,OAAS,SACTC,IAAM,MACNC,OAAS,SACTC,IAAM,MACNC,KAAO,OACPC,KAAO,OACPC,QAAU,UACVC,KAAO,OACPC,OAAS,SACTC,eAAiB,iBACjBC,UAAY,YACZC,YAAc,cACdC,WAAa,aACbC,SAAW,WACXC,MAAQ,QAERC,QAAU,UACVC,OAAS,UAGEC,EAAsB,CACjC5B,OAAS,SACTI,OAAS,iBAGEE,EAAO,CAClBuB,KAAO,OACPC,WAAa,aACbC,KAAO,OACPC,SAAW,WACXC,MAAQ,QACRC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,OAAS,SACTC,QAAU,UACVC,OAAS,SACTC,aAAe,gBAGJC,EAAoB,CAC/BC,QAAU,UACVpC,KAAO,OACPqC,KAAO,OACPvC,OAAS,UAGEwC,EAAmB,CAC9BC,SAAW,SACXC,SAAW,SACXzC,SAAW,WACXD,OAAS,UC7DE2C,EAAW,CACtBhD,EAAQQ,MACRR,EAAQU,WACRV,EAAQW,QACRX,EAAQY,OACRZ,EAAQa,IACRb,EAAQc,OACRd,EAAQe,IACRf,EAAQgB,KACRhB,EAAQiB,KACRjB,EAAQkB,QACRlB,EAAQmB,KACRnB,EAAQoB,OACRpB,EAAQqB,eACRrB,EAAQsB,UACRtB,EAAQuB,YACRvB,EAAQwB,WACRxB,EAAQyB,SACRzB,EAAQ0B,MAER1B,EAAQ4B,OACR5B,EAAQ2B,SCtBGsB,EAAU,CACrBC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAK,MAGMC,EAAO,CAClBnM,GAAK,KACLS,GAAK,KACL2L,GAAK,KACLC,GAAK,KACLjK,GAAK,KACLkK,GAAK,KACLC,GAAK,KACLzJ,GAAK,KACLC,GAAK,KACLC,GAAK,KACLK,GAAK,KACLmJ,GAAK,KACLjI,GAAK,KACLC,GAAK,KACLK,GAAK,KACL4H,GAAK,KACLC,GAAK,KACL1G,GAAK,KACLE,GAAK,KACLY,GAAK,KACLc,GAAK,KACLC,GAAK,KACLW,GAAK,KACLG,GAAK,KACLK,GAAK,KACLE,GAAK,KACLQ,GAAK,KACLE,GAAK,KACLC,GAAK,KACL8C,GAAK,KACLxC,GAAK,KACLK,GAAK,KACLoC,GAAK,KACL/B,GAAK,KACLgC,GAAK,KACLC,GAAK,KACLC,QAAU,WAqNCC,EAA4B,CACvCxN,GAAI,CAAC2M,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCrN,GAAI,CAAC0M,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,GAAIX,EAAKnM,IAC9CN,GAAI,CAACyM,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCnN,GAAI,CAACwM,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrClN,GAAI,CAACuM,EAAKQ,GAAIR,EAAKI,IACnB1M,GAAI,CAACsM,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrChN,GAAI,CAACqM,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC/M,GAAI,CAACoM,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC9M,GAAI,CAACmM,EAAKrJ,GAAIqJ,EAAKI,IACnBtM,GAAI,CAACkM,EAAK/J,GAAI+J,EAAKI,IACnBrM,GAAI,CAACiM,EAAKI,IACVpM,GAAI,CAACgM,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC1M,GAAI,CAAC+L,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCzM,GAAI,CAAC8L,EAAKI,IACVjM,GAAI,CAAC6L,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCvM,GAAI,CAAC4L,EAAKI,GAAIJ,EAAKvE,GAAIuE,EAAK9I,IAC5B7C,GAAI,CAAC2L,EAAK9I,GAAI8I,EAAKI,GAAIJ,EAAKrJ,GAAIqJ,EAAKW,IACrCrM,GAAI,CAAC0L,EAAK1L,GAAI0L,EAAKI,IACnB7L,GAAI,CAACyL,EAAKnM,GAAImM,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IAC9CnM,GAAI,CAACwL,EAAK9I,GAAI8I,EAAKI,GAAIJ,EAAKrJ,GAAIqJ,EAAKW,IACrClM,GAAI,CAACuL,EAAK9I,GAAI8I,EAAKI,GAAIJ,EAAKrJ,GAAIqJ,EAAKW,IACrCjM,GAAI,CAACsL,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrChM,GAAI,CAACqL,EAAKrF,GAAIqF,EAAKI,IACnBxL,GAAI,CAACoL,EAAKrJ,GAAIqJ,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKW,IACrC9L,GAAI,CAACmL,EAAKxD,GAAIwD,EAAKI,IACnBtL,GAAI,CAACkL,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC5L,GAAI,CAACiL,EAAKI,IACVpL,GAAI,CAACgL,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC1L,GAAI,CAAC+K,EAAKI,IACVlL,GAAI,CAAC8K,EAAKI,GAAIJ,EAAKrJ,GAAIqJ,EAAK9I,GAAI8I,EAAKW,IACrCxL,GAAI,CAAC6K,EAAKI,GAAIJ,EAAK9I,IACnB9B,GAAI,CAAC4K,EAAK9I,GAAI8I,EAAKI,GAAIJ,EAAKrJ,GAAIqJ,EAAKW,IACrCtL,GAAI,CAAC2K,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCrL,GAAI,CAAC0K,EAAK/J,GAAI+J,EAAK9I,GAAI8I,EAAKI,IAC5B7K,GAAI,CAACyK,EAAK9I,GAAI8I,EAAKI,IACnB5K,GAAI,CAACwK,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrClL,GAAI,CAACuK,EAAKrJ,GAAIqJ,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKW,IACrCjL,GAAI,CAACsK,EAAK9I,GAAI8I,EAAKI,IACnBzK,GAAI,CAACqK,EAAKW,IACV/K,GAAI,CAACoK,EAAKrJ,GAAIqJ,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKW,IACrC9K,GAAI,CAACmK,EAAKrJ,GAAIqJ,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKW,IACrC7K,GAAI,CAACkK,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC5K,GAAI,CAACiK,EAAKI,IACVpK,GAAI,CAACgK,EAAKC,GAAID,EAAKI,IACnBnK,GAAI,CAAC+J,EAAK/J,GAAI+J,EAAKI,IACnBlK,GAAI,CAAC8J,EAAK9I,GAAI8I,EAAKI,GAAIJ,EAAKrJ,GAAIqJ,EAAKW,IACrCxK,GAAI,CAAC6J,EAAKE,GAAIF,EAAKI,IACnBhK,GAAI,CAAC4J,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCtK,GAAI,CAAC2J,EAAKrJ,GAAIqJ,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKW,IACrCrK,GAAI,CAAC0J,EAAKnM,GAAImM,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IAC9CpK,GAAI,CAACyJ,EAAKrJ,GAAIqJ,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKW,IACrCnK,GAAI,CAACwJ,EAAKpJ,GAAIoJ,EAAKI,GAAIJ,EAAKjD,IAC5BtG,GAAI,CAACuJ,EAAKnM,GAAImM,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IAC9CjK,GAAI,CAACsJ,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrChK,GAAI,CAACqJ,EAAKrJ,GAAIqJ,EAAKI,IACnBxJ,GAAI,CAACoJ,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC9J,GAAI,CAACmJ,EAAKnJ,GAAImJ,EAAKI,IACnBtJ,GAAI,CAACkJ,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC5J,GAAI,CAACiJ,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC3J,GAAI,CAACgJ,EAAKI,IACVnJ,GAAI,CAAC+I,EAAKE,GAAIF,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IAC9CzJ,GAAI,CAAC8I,EAAK9I,GAAI8I,EAAKI,IACnBjJ,GAAI,CAAC6I,EAAK9I,GAAI8I,EAAKI,GAAIJ,EAAKrJ,GAAIqJ,EAAKW,IACrCvJ,GAAI,CAAC4I,EAAKI,IACV/I,GAAI,CAAC2I,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCrJ,GAAI,CAAC0I,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCpJ,GAAI,CAACyI,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCnJ,GAAI,CAACwI,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrClJ,GAAI,CAACuI,EAAKE,GAAIF,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IAC9CjJ,GAAI,CAACsI,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrChJ,GAAI,CAACqI,EAAK9I,GAAI8I,EAAKI,GAAIJ,EAAKrJ,GAAIqJ,EAAKW,IACrC/I,GAAI,CAACoI,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC9I,GAAI,CAACmI,EAAKG,GAAIH,EAAKI,IACnBtI,GAAI,CAACkI,EAAKrJ,GAAIqJ,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKW,IACrC5I,GAAI,CAACiI,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC3I,GAAI,CAACgI,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC1I,GAAI,CAAC+H,EAAKI,GAAIJ,EAAKY,QAASZ,EAAKW,IACjCzI,GAAI,CAAC8H,EAAKrJ,GAAIqJ,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKW,IACrCxI,GAAI,CAAC6H,EAAKI,IACVhI,GAAI,CAAC4H,EAAK5H,GAAI4H,EAAKI,IACnB/H,GAAI,CAAC2H,EAAK3H,GAAI2H,EAAKI,IACnB9H,GAAI,CAAC0H,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCpI,GAAI,CAACyH,EAAKK,GAAIL,EAAKI,IACnB5H,GAAI,CAACwH,EAAKI,IACV3H,GAAI,CAACuH,EAAKI,IACV1H,GAAI,CAACsH,EAAKtH,GAAIsH,EAAKI,IACnBzH,GAAI,CAACqH,EAAKI,GAAIJ,EAAKrJ,GAAIqJ,EAAK9I,GAAI8I,EAAKW,IACrC/H,GAAI,CAACoH,EAAKnM,GAAImM,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IAC9C9H,GAAI,CAACmH,EAAKM,GAAIN,EAAKI,IACnBtH,GAAI,CAACkH,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC5H,GAAI,CAACiH,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC3H,GAAI,CAACgH,EAAKI,IACVnH,GAAI,CAAC+G,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCzH,GAAI,CAAC8G,EAAK9I,GAAI8I,EAAKI,GAAIJ,EAAKrJ,GAAIqJ,EAAKW,IACrCxH,GAAI,CAAC6G,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCvH,GAAI,CAAC4G,EAAKO,GAAIP,EAAKI,IACnB/G,GAAI,CAAC2G,EAAKnM,GAAImM,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IAC9CrH,GAAI,CAAC0G,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCpH,GAAI,CAACyG,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCnH,GAAI,CAACwG,EAAKI,IACV3G,GAAI,CAACuG,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCjH,GAAI,CAACsG,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrChH,GAAI,CAACqG,EAAKzC,GAAIyC,EAAKI,IACnBxG,GAAI,CAACoG,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC9G,GAAI,CAACmG,EAAKnG,GAAImG,EAAKI,GAAIJ,EAAKjD,GAAIiD,EAAKW,IACrC7G,GAAI,CAACkG,EAAKI,GAAIJ,EAAK/J,GAAI+J,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IAC9C5G,GAAI,CAACiG,EAAKjG,GAAIiG,EAAKI,GAAIJ,EAAKjD,IAC5B/C,GAAI,CAACgG,EAAKnM,GAAImM,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IAC9C1G,GAAI,CAAC+F,EAAK9I,GAAI8I,EAAKI,IACnBlG,GAAI,CAAC8F,EAAKI,IACVjG,GAAI,CAAC6F,EAAKI,IACVhG,GAAI,CAAC4F,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCtG,GAAI,CAAC2F,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCrG,GAAI,CAAC0F,EAAKI,IACV7F,GAAI,CAACyF,EAAK9I,GAAI8I,EAAKI,GAAIJ,EAAKrJ,GAAIqJ,EAAKW,IACrCnG,GAAI,CAACwF,EAAKI,IACV3F,GAAI,CAACuF,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCjG,GAAI,CAACsF,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrChG,GAAI,CAACqF,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC/F,GAAI,CAACoF,EAAKI,IACVvF,GAAI,CAACmF,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC7F,GAAI,CAACkF,EAAKI,IACVrF,GAAI,CAACiF,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC3F,GAAI,CAACgF,EAAKrJ,GAAIqJ,EAAKI,IACnBnF,GAAI,CAAC+E,EAAKrF,GAAIqF,EAAKI,IACnBlF,GAAI,CAAC8E,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCxF,GAAI,CAAC6E,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCvF,GAAI,CAAC4E,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCtF,GAAI,CAAC2E,EAAK9I,GAAI8I,EAAKI,GAAIJ,EAAKrJ,GAAIqJ,EAAKW,IACrCrF,GAAI,CAAC0E,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCpF,GAAI,CAACyE,EAAKI,IACV5E,GAAI,CAACwE,EAAKrJ,GAAIqJ,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKW,IACrClF,GAAI,CAACuE,EAAKvE,GAAIuE,EAAKI,IACnB1E,GAAI,CAACsE,EAAKtE,GAAIsE,EAAKI,IACnBzE,GAAI,CAACqE,EAAKI,IACVxE,GAAI,CAACoE,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC9E,GAAI,CAACmE,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC7E,GAAI,CAACkE,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC5E,GAAI,CAACiE,EAAKnM,GAAImM,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IAC9C3E,GAAI,CAACgE,EAAKrJ,GAAIqJ,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKW,IACrC1E,GAAI,CAAC+D,EAAKrJ,GAAIqJ,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKW,IACrCzE,GAAI,CAAC8D,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCxE,GAAI,CAAC6D,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCvE,GAAI,CAAC4D,EAAKS,GAAIT,EAAKI,IACnB/D,GAAI,CAAC2D,EAAK3D,GAAI2D,EAAKI,IACnB9D,GAAI,CAAC0D,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCpE,GAAI,CAACyD,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCnE,GAAI,CAACwD,EAAKxD,GAAIwD,EAAKI,IACnB3D,GAAI,CAACuD,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCjE,GAAI,CAACsD,EAAKrJ,GAAIqJ,EAAKI,IACnBzD,GAAI,CAACqD,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,GAAIX,EAAKnM,IAC9C+I,GAAI,CAACoD,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC9D,GAAI,CAACmD,EAAKnD,GAAImD,EAAKI,IACnBtD,GAAI,CAACkD,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC5D,GAAI,CAACiD,EAAKjD,GAAIiD,EAAKI,IACnBpD,GAAI,CAACgD,EAAK9I,GAAI8I,EAAKI,GAAIJ,EAAKrJ,GAAIqJ,EAAKW,IACrC1D,GAAI,CAAC+C,EAAKnM,GAAImM,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IAC9CzD,GAAI,CAAC8C,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCxD,GAAI,CAAC6C,EAAK9I,GAAI8I,EAAKI,GAAIJ,EAAKrJ,GAAIqJ,EAAKW,IACrCvD,GAAI,CAAC4C,EAAKhC,GAAIgC,EAAKI,IACnB/C,GAAI,CAAC2C,EAAKI,IACV9C,GAAI,CAAC0C,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCpD,GAAI,CAACyC,EAAKtC,GAAIsC,EAAKI,IACnB5C,GAAI,CAACwC,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrClD,GAAI,CAACuC,EAAKvC,GAAIuC,EAAKI,IACnB1C,GAAI,CAACsC,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrChD,GAAI,CAACqC,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC/C,GAAI,CAACoC,EAAK9I,GAAI8I,EAAKI,GAAIJ,EAAKrJ,GAAIqJ,EAAKW,IACrC9C,GAAI,CAACmC,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC7C,GAAI,CAACkC,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC5C,GAAI,CAACiC,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC3C,GAAI,CAACgC,EAAKrJ,GAAIqJ,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKW,IACrC1C,GAAI,CAAC+B,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCzC,GAAI,CAAC8B,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCxC,GAAI,CAAC6B,EAAK9I,GAAI8I,EAAKI,GAAIJ,EAAKrJ,GAAIqJ,EAAKW,IACrCvC,GAAI,CAAC4B,EAAK9I,GAAI8I,EAAKI,GAAIJ,EAAKrJ,GAAIqJ,EAAKW,IACrCtC,GAAI,CAAC2B,EAAK3B,GAAI2B,EAAKI,IACnB9B,GAAI,CAAC0B,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCpC,GAAI,CAACyB,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCnC,GAAI,CAACwB,EAAKnM,GAAImM,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IAC9ClC,GAAI,CAACuB,EAAKI,IACV1B,GAAI,CAACsB,EAAKtB,GAAIsB,EAAKI,IACnBzB,GAAI,CAACqB,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC/B,GAAI,CAACoB,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC9B,GAAI,CAACmB,EAAKY,QAASZ,EAAKW,GAAIX,EAAKI,IACjCtB,GAAI,CAACkB,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC5B,GAAI,CAACiB,EAAKI,GAAIJ,EAAKjD,GAAIiD,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IAC9C3B,GAAI,CAACgB,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrC1B,GAAI,CAACe,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCzB,GAAI,CAACc,EAAKrJ,GAAIqJ,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKW,IACrCxB,GAAI,CAACa,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCvB,GAAI,CAACY,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCtB,GAAI,CAACW,EAAKrJ,GAAIqJ,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKW,IACrCrB,GAAI,CAACU,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCpB,GAAI,CAACS,EAAKU,GAAIV,EAAKI,IACnBZ,GAAI,CAACQ,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrClB,GAAI,CAACO,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCjB,GAAI,CAACM,EAAKI,IACVT,GAAI,CAACK,EAAKnM,GAAImM,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IAC9Cf,GAAI,CAACI,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCd,GAAI,CAACG,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCb,GAAI,CAACE,EAAKI,GAAIJ,EAAK9I,GAAI8I,EAAKrJ,GAAIqJ,EAAKW,IACrCZ,GAAI,CAACC,EAAKI,KCppBCU,EAAS,CACpBC,QAAU,UACVC,UAAY,YACZC,MAAQ,QACRC,SAAW,WACXC,aAAe,gBAGJC,EAAS,CACpBC,MAAO,EACPC,OAAQ,GAGGC,EAAQ,CACnBF,MAAO,EACPC,OAAQ,GAGGE,EAAW,CACtBC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,MACNC,IAAM,OClKKC,EAAY,UACZC,EAAuB,qBAEvBC,EAAe,CAC1BC,OAAS,cACTC,eAAiB,sBACjBC,mBAAqB,0BACrBC,aAAe,oBACfC,UAAY,iBACZC,WAAa,kBACbC,eAAiB,sBACjBC,YAAc,mBACdC,UAAY,iBACZC,UAAY,iBACZC,uBACG,8BACHC,gBAAkB,uBAClBC,uBACG,8BACHC,UAAY,wBACZC,mBAAqB,0BACrBC,WAAa,kBACbC,qBACG,4BACHC,cAAgB,sBAKLC,EAAsBnB,EAEtBoB,EAAiB,CAC5BC,WAAa,aAEbC,IAAM,MACNC,MAAQ,QACRC,UAAY,YAEZC,UAAY,YACZjB,YAAc,cAEdkB,OAAS,SAET7K,SAAW,WACXV,OAAS,SACTM,OAAS,SACTG,MAAQ,QAER+K,cAAgB,gBAEhBC,eAAiB,iBACjBC,gBAAkB,kBAClBC,aAAe,eAEfC,iBAAmB,mBACnBf,WAAa,aACbgB,YAAc,cACdC,aAAe,eACfC,QAAU,WAGCb,EAAa,CACxBc,QAAU,UACVC,YAAc,cACdC,eAAiB,iBACjBC,cAAgB,iBAGLf,EAAQ,CACnB7K,MAAO,EACPC,OAAQ,GAGG4L,EAAa,CACxB7L,KAAO,OACPC,MAAQ,SAGG6L,EAAW,UAEXC,EAAW,CACtBC,KAAO,OACPC,MAAQ,SAGGC,EAAa,CACxBC,KAAO,OACPC,QAAU,UACVC,KAAO,OACPC,SAAW,WACXC,gBAAkB,kBAClBC,eAAiB,iBACjBC,gBAAkB,kBAClBC,UAAY,aAGDC,EAAkB,GAElBC,EAAsB,CACjCC,UAAY,aC/FDC,EAAkB/a,EAAQ6L,GAC1BmP,EAAmB5M,EAASmI,IAC5B0E,EAAiBvN,EAAOC,QACxBuN,EAAiBlN,EAAOC,KACxBkN,EAAsBnN,EAAOC,KAC7BmN,EAAyBpN,EAAOC,KAChCoN,EAAgBlN,EAAMD,MACtBoN,EAAqB1C,EAAWc,QAChC6B,EAAgBzC,EAAM5K,MCZtB2K,EAAM,CACjB2C,MAAQ,QACRC,MAAQ,QACRC,QAAU,UACVC,WAAa,aACbC,KAAO,QAGIC,EAAa,CACxBC,QAAU,UACVC,IAAM,OCVKC,EAAa,CACxBC,iBAAmB,oBCDRC,EAAW,CACtBC,cAAgB,gBAChBC,sBAAwB,wBACxBC,yCACG,2CACHC,qBAAuB,uBACvBC,wBACG,0BACHC,qBAAuB,uBACvBC,wBACG,0BACHC,6BACG,+BACHC,0BACG,4BACHC,oBAAsB,sBACtBC,mBAAqB,oBACrBC,cAAgB,gBAChBC,YAAc,cACdC,eAAiB,iBACjB9D,cAAgB,cAChB+D,aAAe,eACfC,eAAiB,iBACjBC,aAAe,eACfC,eAAiB,0BACjBpE,UAAY,YACZqE,eAAiB,yBACjBC,WAAa,aACbC,aAAe,eACfC,kBAAoB,oBACpBC,iBAAmB,mBACnBC,kBAAoB,oBACpBC,YAAc,yBACdtE,aAAe,eACfD,gBAAkB,kBAClB4C,WAAa,iBACb4B,WAAa,iBACbC,WAAa,aACbC,sBACG,6BACHC,gBAAkB,aAClBC,qBACG,4BACHC,KAAO,YACPC,MAAQ,QACRC,QAAU,UACVC,4BACG,8BACHC,KAAO,OACPC,cAAgB,yBAChBC,aAAe,2BACfC,iBAAmB,mBACnBC,uBAAyB,yBACzBC,SAAW,WACX5G,eAAiB,iBACjBmB,OAAS,SACT0F,gBAAkB,kBAClBC,mBAAqB,qBACrBC,yBAA2B,OAC3BC,gBAAkB,SAClBC,KAAO,YACPC,eAAiB,iBACjB/G,UAAY,eACZC,uBAAyB,UACzB+G,OAAS,SACTC,QAAU,UACVC,aAAe,eACfC,0BACG,4BACH/E,QAAU,UACVgF,oBAAsB,sBACtBC,QAAU,cACVC,gBAAqC,kBACrCC,kBAAoB,oBACpBC,UAAY,YACZC,gBAAkB,kBAClBtH,uBAAyB,yBACzBuH,cAAgB,gBAChBC,SAAW,WACXC,YAAc,cACdC,YACG,mCACHC,UAAY,YACZC,YAAc,kBACdC,8BACG,gCACHC,UAAY,YACZC,MAAQ,aACRC,cAAgB,gBAChBC,UAAY,IACZC,MAAQ,QACRC,WAAa,kBACbC,gBAAkB,kBAClBC,eAAiB,gBACjBC,YAAc,cACdC,WAAa,aACbC,qBAAuB,uBACvBnH,QAAU,qBACVoH,2BACG,6BACHC,mBAAqB,qBACrB1S,SAAW,WACXoJ,OAAS,UAGEuJ,EAAmB,CAC9B/S,OAAS,SACTgT,SAAW,YAGAC,EAAmB,CAC9BC,aAAe,YAGJC,EAAY,CACvBD,aAAe,gBAGJE,EAAgB,CAC3BF,aAAe,gBCvHJG,EAA4B,eAE5BC,EAAwB,CACnCC,aAAc,MACdC,cAAe,KCJJC,EAAW,CACtBC,QAAU,UACVC,OAAS,UCFEC,GAAQ", "file": "paypal-sdk-constants.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"ppsdkconstants\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ppsdkconstants\"] = factory();\n\telse\n\t\troot[\"ppsdkconstants\"] = factory();\n})((typeof self !== 'undefined' ? self : this), function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 0);\n", "/* @flow */\n\nexport const FUNDING = {\n  PAYPAL: (\"paypal\": \"paypal\"),\n  VENMO: (\"venmo\": \"venmo\"),\n  APPLEPAY: (\"applepay\": \"applepay\"),\n  ITAU: (\"itau\": \"itau\"),\n  CREDIT: (\"credit\": \"credit\"),\n  PAYLATER: (\"paylater\": \"paylater\"),\n  CARD: (\"card\": \"card\"),\n  IDEAL: (\"ideal\": \"ideal\"),\n  SEPA: (\"sepa\": \"sepa\"),\n  BANCONTACT: (\"bancontact\": \"bancontact\"),\n  GIROPAY: (\"giropay\": \"giropay\"),\n  SOFORT: (\"sofort\": \"sofort\"),\n  EPS: (\"eps\": \"eps\"),\n  MYBANK: (\"mybank\": \"mybank\"),\n  P24: (\"p24\": \"p24\"),\n  PAYU: (\"payu\": \"payu\"),\n  BLIK: (\"blik\": \"blik\"),\n  TRUSTLY: (\"trustly\": \"trustly\"),\n  OXXO: (\"oxxo\": \"oxxo\"),\n  BOLETO: (\"boleto\": \"boleto\"),\n  BOLETOBANCARIO: (\"boletobancario\": \"boletobancario\"),\n  WECHATPAY: (\"wechatpay\": \"wechatpay\"),\n  MERCADOPAGO: (\"mercadopago\": \"mercadopago\"),\n  MULTIBANCO: (\"multibanco\": \"multibanco\"),\n  SATISPAY: (\"satispay\": \"satispay\"),\n  PAIDY: (\"paidy\": \"paidy\"),\n  // deprecated APMs will be removed soon\n  ZIMPLER: (\"zimpler\": \"zimpler\"),\n  MAXIMA: (\"maxima\": \"maxima\"),\n};\n\nexport const FUNDING_BRAND_LABEL = {\n  PAYPAL: (\"PayPal\": \"PayPal\"),\n  CREDIT: (\"PayPal Credit\": \"PayPal Credit\"),\n};\n\nexport const CARD = {\n  VISA: (\"visa\": \"visa\"),\n  MASTERCARD: (\"mastercard\": \"mastercard\"),\n  AMEX: (\"amex\": \"amex\"),\n  DISCOVER: (\"discover\": \"discover\"),\n  HIPER: (\"hiper\": \"hiper\"),\n  ELO: (\"elo\": \"elo\"),\n  JCB: (\"jcb\": \"jcb\"),\n  CUP: (\"cup\": \"cup\"),\n  DINERS: (\"diners\": \"diners\"),\n  MAESTRO: (\"maestro\": \"maestro\"),\n  EFTPOS: (\"eftpos\": \"eftpos\"),\n  CB_NATIONALE: (\"cb_nationale\": \"cb_nationale\"),\n};\n\nexport const WALLET_INSTRUMENT = {\n  BALANCE: (\"balance\": \"balance\"),\n  CARD: (\"card\": \"card\"),\n  BANK: (\"bank\": \"bank\"),\n  CREDIT: (\"credit\": \"credit\"),\n};\n\nexport const FUNDING_PRODUCTS = {\n  PAY_IN_3: (\"payIn3\": \"payIn3\"),\n  PAY_IN_4: (\"payIn4\": \"payIn4\"),\n  PAYLATER: (\"paylater\": \"paylater\"),\n  CREDIT: (\"credit\": \"credit\"),\n};\n", "/* @flow */\n\nimport { FUNDING } from \"./funding\";\n\nexport const APM_LIST = [\n  FUNDING.IDEAL,\n  FUNDING.BANCONTACT,\n  FUNDING.GIROPAY,\n  FUNDING.SOFORT,\n  FUNDING.EPS,\n  FUNDING.MYBANK,\n  FUNDING.P24,\n  FUNDING.PAYU,\n  FUNDING.BLIK,\n  FUNDING.TRUSTLY,\n  FUNDING.OXXO,\n  FUNDING.BOLETO,\n  FUNDING.B<PERSON><PERSON><PERSON>BANCARIO,\n  FUNDING.WECHATPAY,\n  FUNDING.MERCADOPAGO,\n  FUNDING.MULTIBANCO,\n  FUNDING.SATISPAY,\n  FUNDING.PAIDY,\n  // deprecated APMs will be removed soon\n  FUNDING.MAXIMA,\n  FUNDING.ZIMPLER,\n];\n", "/* @flow */\n/* eslint max-lines: 0 */\n\nexport const COUNTRY = {\n  AD: (\"AD\": \"AD\"),\n  AE: (\"AE\": \"AE\"),\n  AG: (\"AG\": \"AG\"),\n  AI: (\"AI\": \"AI\"),\n  AL: (\"AL\": \"AL\"),\n  AM: (\"AM\": \"AM\"),\n  AN: (\"AN\": \"AN\"),\n  AO: (\"AO\": \"AO\"),\n  AR: (\"AR\": \"AR\"),\n  AT: (\"AT\": \"AT\"),\n  AU: (\"AU\": \"AU\"),\n  AW: (\"AW\": \"AW\"),\n  AZ: (\"AZ\": \"AZ\"),\n  BA: (\"BA\": \"BA\"),\n  BB: (\"BB\": \"BB\"),\n  BE: (\"BE\": \"BE\"),\n  BF: (\"BF\": \"BF\"),\n  BG: (\"BG\": \"BG\"),\n  BH: (\"BH\": \"BH\"),\n  BI: (\"BI\": \"BI\"),\n  BJ: (\"BJ\": \"BJ\"),\n  BM: (\"BM\": \"BM\"),\n  BN: (\"BN\": \"BN\"),\n  BO: (\"BO\": \"BO\"),\n  BR: (\"BR\": \"BR\"),\n  BS: (\"BS\": \"BS\"),\n  BT: (\"BT\": \"BT\"),\n  BW: (\"BW\": \"BW\"),\n  BY: (\"BY\": \"BY\"),\n  BZ: (\"BZ\": \"BZ\"),\n  CA: (\"CA\": \"CA\"),\n  CD: (\"CD\": \"CD\"),\n  CG: (\"CG\": \"CG\"),\n  CH: (\"CH\": \"CH\"),\n  CI: (\"CI\": \"CI\"),\n  CK: (\"CK\": \"CK\"),\n  CL: (\"CL\": \"CL\"),\n  CM: (\"CM\": \"CM\"),\n  CN: (\"CN\": \"CN\"),\n  CO: (\"CO\": \"CO\"),\n  CR: (\"CR\": \"CR\"),\n  CV: (\"CV\": \"CV\"),\n  CY: (\"CY\": \"CY\"),\n  CZ: (\"CZ\": \"CZ\"),\n  DE: (\"DE\": \"DE\"),\n  DJ: (\"DJ\": \"DJ\"),\n  DK: (\"DK\": \"DK\"),\n  DM: (\"DM\": \"DM\"),\n  DO: (\"DO\": \"DO\"),\n  DZ: (\"DZ\": \"DZ\"),\n  EC: (\"EC\": \"EC\"),\n  EE: (\"EE\": \"EE\"),\n  EG: (\"EG\": \"EG\"),\n  ER: (\"ER\": \"ER\"),\n  ES: (\"ES\": \"ES\"),\n  ET: (\"ET\": \"ET\"),\n  FI: (\"FI\": \"FI\"),\n  FJ: (\"FJ\": \"FJ\"),\n  FK: (\"FK\": \"FK\"),\n  FM: (\"FM\": \"FM\"),\n  FO: (\"FO\": \"FO\"),\n  FR: (\"FR\": \"FR\"),\n  GA: (\"GA\": \"GA\"),\n  GB: (\"GB\": \"GB\"),\n  GD: (\"GD\": \"GD\"),\n  GE: (\"GE\": \"GE\"),\n  GF: (\"GF\": \"GF\"),\n  GI: (\"GI\": \"GI\"),\n  GL: (\"GL\": \"GL\"),\n  GM: (\"GM\": \"GM\"),\n  GN: (\"GN\": \"GN\"),\n  GP: (\"GP\": \"GP\"),\n  GR: (\"GR\": \"GR\"),\n  GT: (\"GT\": \"GT\"),\n  GW: (\"GW\": \"GW\"),\n  GY: (\"GY\": \"GY\"),\n  HK: (\"HK\": \"HK\"),\n  HN: (\"HN\": \"HN\"),\n  HR: (\"HR\": \"HR\"),\n  HU: (\"HU\": \"HU\"),\n  ID: (\"ID\": \"ID\"),\n  IE: (\"IE\": \"IE\"),\n  IL: (\"IL\": \"IL\"),\n  IN: (\"IN\": \"IN\"),\n  IS: (\"IS\": \"IS\"),\n  IT: (\"IT\": \"IT\"),\n  JM: (\"JM\": \"JM\"),\n  JO: (\"JO\": \"JO\"),\n  JP: (\"JP\": \"JP\"),\n  KE: (\"KE\": \"KE\"),\n  KG: (\"KG\": \"KG\"),\n  KH: (\"KH\": \"KH\"),\n  KI: (\"KI\": \"KI\"),\n  KM: (\"KM\": \"KM\"),\n  KN: (\"KN\": \"KN\"),\n  KR: (\"KR\": \"KR\"),\n  KW: (\"KW\": \"KW\"),\n  KY: (\"KY\": \"KY\"),\n  KZ: (\"KZ\": \"KZ\"),\n  LA: (\"LA\": \"LA\"),\n  LC: (\"LC\": \"LC\"),\n  LI: (\"LI\": \"LI\"),\n  LK: (\"LK\": \"LK\"),\n  LS: (\"LS\": \"LS\"),\n  LT: (\"LT\": \"LT\"),\n  LU: (\"LU\": \"LU\"),\n  LV: (\"LV\": \"LV\"),\n  MA: (\"MA\": \"MA\"),\n  MC: (\"MC\": \"MC\"),\n  MD: (\"MD\": \"MD\"),\n  ME: (\"ME\": \"ME\"),\n  MG: (\"MG\": \"MG\"),\n  MH: (\"MH\": \"MH\"),\n  MK: (\"MK\": \"MK\"),\n  ML: (\"ML\": \"ML\"),\n  MN: (\"MN\": \"MN\"),\n  MQ: (\"MQ\": \"MQ\"),\n  MR: (\"MR\": \"MR\"),\n  MS: (\"MS\": \"MS\"),\n  MT: (\"MT\": \"MT\"),\n  MU: (\"MU\": \"MU\"),\n  MV: (\"MV\": \"MV\"),\n  MW: (\"MW\": \"MW\"),\n  MX: (\"MX\": \"MX\"),\n  MY: (\"MY\": \"MY\"),\n  MZ: (\"MZ\": \"MZ\"),\n  NA: (\"NA\": \"NA\"),\n  NC: (\"NC\": \"NC\"),\n  NE: (\"NE\": \"NE\"),\n  NF: (\"NF\": \"NF\"),\n  NG: (\"NG\": \"NG\"),\n  NI: (\"NI\": \"NI\"),\n  NL: (\"NL\": \"NL\"),\n  NO: (\"NO\": \"NO\"),\n  NP: (\"NP\": \"NP\"),\n  NR: (\"NR\": \"NR\"),\n  NU: (\"NU\": \"NU\"),\n  NZ: (\"NZ\": \"NZ\"),\n  OM: (\"OM\": \"OM\"),\n  PA: (\"PA\": \"PA\"),\n  PE: (\"PE\": \"PE\"),\n  PF: (\"PF\": \"PF\"),\n  PG: (\"PG\": \"PG\"),\n  PH: (\"PH\": \"PH\"),\n  PL: (\"PL\": \"PL\"),\n  PM: (\"PM\": \"PM\"),\n  PN: (\"PN\": \"PN\"),\n  PT: (\"PT\": \"PT\"),\n  PW: (\"PW\": \"PW\"),\n  PY: (\"PY\": \"PY\"),\n  QA: (\"QA\": \"QA\"),\n  RE: (\"RE\": \"RE\"),\n  RO: (\"RO\": \"RO\"),\n  RS: (\"RS\": \"RS\"),\n  RU: (\"RU\": \"RU\"),\n  RW: (\"RW\": \"RW\"),\n  SA: (\"SA\": \"SA\"),\n  SB: (\"SB\": \"SB\"),\n  SC: (\"SC\": \"SC\"),\n  SE: (\"SE\": \"SE\"),\n  SG: (\"SG\": \"SG\"),\n  SH: (\"SH\": \"SH\"),\n  SI: (\"SI\": \"SI\"),\n  SJ: (\"SJ\": \"SJ\"),\n  SK: (\"SK\": \"SK\"),\n  SL: (\"SL\": \"SL\"),\n  SM: (\"SM\": \"SM\"),\n  SN: (\"SN\": \"SN\"),\n  SO: (\"SO\": \"SO\"),\n  SR: (\"SR\": \"SR\"),\n  ST: (\"ST\": \"ST\"),\n  SV: (\"SV\": \"SV\"),\n  SZ: (\"SZ\": \"SZ\"),\n  TC: (\"TC\": \"TC\"),\n  TD: (\"TD\": \"TD\"),\n  TG: (\"TG\": \"TG\"),\n  TH: (\"TH\": \"TH\"),\n  TJ: (\"TJ\": \"TJ\"),\n  TM: (\"TM\": \"TM\"),\n  TN: (\"TN\": \"TN\"),\n  TO: (\"TO\": \"TO\"),\n  TR: (\"TR\": \"TR\"),\n  TT: (\"TT\": \"TT\"),\n  TV: (\"TV\": \"TV\"),\n  TW: (\"TW\": \"TW\"),\n  TZ: (\"TZ\": \"TZ\"),\n  UA: (\"UA\": \"UA\"),\n  UG: (\"UG\": \"UG\"),\n  US: (\"US\": \"US\"),\n  UY: (\"UY\": \"UY\"),\n  VA: (\"VA\": \"VA\"),\n  VC: (\"VC\": \"VC\"),\n  VE: (\"VE\": \"VE\"),\n  VG: (\"VG\": \"VG\"),\n  VN: (\"VN\": \"VN\"),\n  VU: (\"VU\": \"VU\"),\n  WF: (\"WF\": \"WF\"),\n  WS: (\"WS\": \"WS\"),\n  YE: (\"YE\": \"YE\"),\n  YT: (\"YT\": \"YT\"),\n  ZA: (\"ZA\": \"ZA\"),\n  ZM: (\"ZM\": \"ZM\"),\n  ZW: (\"ZW\": \"ZW\"),\n};\n\nexport const LANG = {\n  AR: (\"ar\": \"ar\"),\n  BG: (\"bg\": \"bg\"),\n  CS: (\"cs\": \"cs\"),\n  DA: (\"da\": \"da\"),\n  DE: (\"de\": \"de\"),\n  EL: (\"el\": \"el\"),\n  EN: (\"en\": \"en\"),\n  ES: (\"es\": \"es\"),\n  ET: (\"et\": \"et\"),\n  FI: (\"fi\": \"fi\"),\n  FR: (\"fr\": \"fr\"),\n  HE: (\"he\": \"he\"),\n  HU: (\"hu\": \"hu\"),\n  ID: (\"id\": \"id\"),\n  IT: (\"it\": \"it\"),\n  JA: (\"ja\": \"ja\"),\n  KO: (\"ko\": \"ko\"),\n  LT: (\"lt\": \"lt\"),\n  LV: (\"lv\": \"lv\"),\n  MS: (\"ms\": \"ms\"),\n  NL: (\"nl\": \"nl\"),\n  NO: (\"no\": \"no\"),\n  PL: (\"pl\": \"pl\"),\n  PT: (\"pt\": \"pt\"),\n  RO: (\"ro\": \"ro\"),\n  RU: (\"ru\": \"ru\"),\n  SI: (\"si\": \"si\"),\n  SK: (\"sk\": \"sk\"),\n  SL: (\"sl\": \"sl\"),\n  SQ: (\"sq\": \"sq\"),\n  SV: (\"sv\": \"sv\"),\n  TH: (\"th\": \"th\"),\n  TL: (\"tl\": \"tl\"),\n  TR: (\"tr\": \"tr\"),\n  VI: (\"vi\": \"vi\"),\n  ZH: (\"zh\": \"zh\"),\n  ZH_HANT: (\"zh_Hant\": \"zh_Hant\"),\n};\n\nexport type CountryLangs = {|\n  AD: \"en\" | \"fr\" | \"es\" | \"zh\",\n  AE: \"en\" | \"fr\" | \"es\" | \"zh\" | \"ar\",\n  AG: \"en\" | \"fr\" | \"es\" | \"zh\",\n  AI: \"en\" | \"fr\" | \"es\" | \"zh\",\n  AL: \"sq\" | \"en\",\n  AM: \"en\" | \"fr\" | \"es\" | \"zh\",\n  AN: \"en\" | \"fr\" | \"es\" | \"zh\",\n  AO: \"en\" | \"fr\" | \"es\" | \"zh\",\n  AR: \"es\" | \"en\",\n  AT: \"de\" | \"en\",\n  AU: \"en\",\n  AW: \"en\" | \"fr\" | \"es\" | \"zh\",\n  AZ: \"en\" | \"fr\" | \"es\" | \"zh\",\n  BA: \"en\",\n  BB: \"en\" | \"fr\" | \"es\" | \"zh\",\n  BE: \"en\" | \"nl\" | \"fr\",\n  BF: \"fr\" | \"en\" | \"es\" | \"zh\",\n  BG: \"bg\" | \"en\",\n  BH: \"ar\" | \"en\" | \"fr\" | \"es\" | \"zh\",\n  BI: \"fr\" | \"en\" | \"es\" | \"zh\",\n  BJ: \"fr\" | \"en\" | \"es\" | \"zh\",\n  BM: \"en\" | \"fr\" | \"es\" | \"zh\",\n  BN: \"ms\" | \"en\",\n  BO: \"es\" | \"en\" | \"fr\" | \"zh\",\n  BR: \"pt\" | \"en\",\n  BS: \"en\" | \"fr\" | \"es\" | \"zh\",\n  BT: \"en\",\n  BW: \"en\" | \"fr\" | \"es\" | \"zh\",\n  BY: \"en\",\n  BZ: \"en\" | \"es\" | \"fr\" | \"zh\",\n  CA: \"en\" | \"fr\",\n  CD: \"fr\" | \"en\" | \"es\" | \"zh\",\n  CG: \"en\" | \"fr\" | \"es\" | \"zh\",\n  CH: \"de\" | \"fr\" | \"en\",\n  CI: \"fr\" | \"en\",\n  CK: \"en\" | \"fr\" | \"es\" | \"zh\",\n  CL: \"es\" | \"en\" | \"fr\" | \"zh\",\n  CM: \"fr\" | \"en\",\n  CN: \"zh\",\n  CO: \"es\" | \"en\" | \"fr\" | \"zh\",\n  CR: \"es\" | \"en\" | \"fr\" | \"zh\",\n  CV: \"en\" | \"fr\" | \"es\" | \"zh\",\n  CY: \"en\",\n  CZ: \"cs\" | \"en\",\n  DE: \"de\" | \"en\",\n  DJ: \"fr\" | \"en\" | \"es\" | \"zh\",\n  DK: \"da\" | \"en\",\n  DM: \"en\" | \"fr\" | \"es\" | \"zh\",\n  DO: \"es\" | \"en\" | \"fr\" | \"zh\",\n  DZ: \"ar\" | \"en\" | \"fr\" | \"es\" | \"zh\",\n  EC: \"es\" | \"en\" | \"fr\" | \"zh\",\n  EE: \"et\" | \"en\" | \"ru\",\n  EG: \"ar\" | \"en\" | \"fr\" | \"es\" | \"zh\",\n  ER: \"en\" | \"fr\" | \"es\" | \"zh\",\n  ES: \"es\" | \"en\",\n  ET: \"en\" | \"fr\" | \"es\" | \"zh\",\n  FI: \"fi\" | \"en\",\n  FJ: \"en\" | \"fr\" | \"es\" | \"zh\",\n  FK: \"en\" | \"fr\" | \"es\" | \"zh\",\n  FM: \"en\",\n  FO: \"da\" | \"en\" | \"fr\" | \"es\" | \"zh\",\n  FR: \"fr\" | \"en\",\n  GA: \"fr\" | \"en\" | \"es\" | \"zh\",\n  GB: \"en\",\n  GD: \"en\" | \"fr\" | \"es\" | \"zh\",\n  GE: \"en\" | \"fr\" | \"es\" | \"zh\",\n  GF: \"en\" | \"fr\" | \"es\" | \"zh\",\n  GI: \"en\" | \"fr\" | \"es\" | \"zh\",\n  GL: \"da\" | \"en\" | \"fr\" | \"es\" | \"zh\",\n  GM: \"en\" | \"fr\" | \"es\" | \"zh\",\n  GN: \"fr\" | \"en\" | \"es\" | \"zh\",\n  GP: \"en\" | \"fr\" | \"es\" | \"zh\",\n  GR: \"el\" | \"en\",\n  GT: \"es\" | \"en\" | \"fr\" | \"zh\",\n  GW: \"en\" | \"fr\" | \"es\" | \"zh\",\n  GY: \"en\" | \"fr\" | \"es\" | \"zh\",\n  HK: \"en\" | \"zh_Hant\",\n  HN: \"es\" | \"en\" | \"fr\" | \"zh\",\n  HR: \"en\",\n  HU: \"hu\" | \"en\",\n  ID: \"id\" | \"en\",\n  IE: \"en\" | \"fr\" | \"es\" | \"zh\",\n  IL: \"he\" | \"en\",\n  IN: \"en\",\n  IS: \"en\",\n  IT: \"it\" | \"en\",\n  JM: \"en\" | \"es\" | \"fr\" | \"zh\",\n  JO: \"ar\" | \"en\" | \"fr\" | \"es\" | \"zh\",\n  JP: \"ja\" | \"en\",\n  KE: \"en\" | \"fr\" | \"es\" | \"zh\",\n  KG: \"en\" | \"fr\" | \"es\" | \"zh\",\n  KH: \"en\",\n  KI: \"en\" | \"fr\" | \"es\" | \"zh\",\n  KM: \"fr\" | \"en\" | \"es\" | \"zh\",\n  KN: \"en\" | \"fr\" | \"es\" | \"zh\",\n  KR: \"ko\" | \"en\",\n  KW: \"ar\" | \"en\" | \"fr\" | \"es\" | \"zh\",\n  KY: \"en\" | \"fr\" | \"es\" | \"zh\",\n  KZ: \"en\" | \"fr\" | \"es\" | \"zh\",\n  LA: \"en\",\n  LC: \"en\" | \"fr\" | \"es\" | \"zh\",\n  LI: \"en\" | \"fr\" | \"es\" | \"zh\",\n  LK: \"si\" | \"en\",\n  LS: \"en\" | \"fr\" | \"es\" | \"zh\",\n  LT: \"lt\" | \"en\" | \"ru\" | \"zh\",\n  LU: \"en\" | \"de\" | \"fr\" | \"es\" | \"zh\",\n  LV: \"lv\" | \"en\" | \"ru\",\n  MA: \"ar\" | \"en\" | \"fr\" | \"es\" | \"zh\",\n  MC: \"fr\" | \"en\",\n  MD: \"en\",\n  ME: \"en\",\n  MG: \"en\" | \"fr\" | \"es\" | \"zh\",\n  MH: \"en\" | \"fr\" | \"es\" | \"zh\",\n  MK: \"en\",\n  ML: \"fr\" | \"en\" | \"es\" | \"zh\",\n  MN: \"en\",\n  MQ: \"en\" | \"fr\" | \"es\" | \"zh\",\n  MR: \"en\" | \"fr\" | \"es\" | \"zh\",\n  MS: \"en\" | \"fr\" | \"es\" | \"zh\",\n  MT: \"en\",\n  MU: \"en\" | \"fr\" | \"es\" | \"zh\",\n  MV: \"en\",\n  MW: \"en\" | \"fr\" | \"es\" | \"zh\",\n  MX: \"es\" | \"en\",\n  MY: \"ms\" | \"en\",\n  MZ: \"en\" | \"fr\" | \"es\" | \"zh\",\n  NA: \"en\" | \"fr\" | \"es\" | \"zh\",\n  NC: \"en\" | \"fr\" | \"es\" | \"zh\",\n  NE: \"fr\" | \"en\" | \"es\" | \"zh\",\n  NF: \"en\" | \"fr\" | \"es\" | \"zh\",\n  NG: \"en\",\n  NI: \"es\" | \"en\" | \"fr\" | \"zh\",\n  NL: \"nl\" | \"en\",\n  NO: \"no\" | \"en\",\n  NP: \"en\",\n  NR: \"en\" | \"fr\" | \"es\" | \"zh\",\n  NU: \"en\" | \"fr\" | \"es\" | \"zh\",\n  NZ: \"en\" | \"fr\" | \"es\" | \"zh\",\n  OM: \"ar\" | \"en\" | \"fr\" | \"es\" | \"zh\",\n  PA: \"es\" | \"en\" | \"fr\" | \"zh\",\n  PE: \"es\" | \"en\" | \"fr\" | \"zh\",\n  PF: \"en\" | \"fr\" | \"es\" | \"zh\",\n  PG: \"en\" | \"fr\" | \"es\" | \"zh\",\n  PH: \"tl\" | \"en\",\n  PL: \"pl\" | \"en\",\n  PM: \"en\" | \"fr\" | \"es\" | \"zh\",\n  PN: \"en\" | \"fr\" | \"es\" | \"zh\",\n  PT: \"pt\" | \"en\",\n  PW: \"en\" | \"fr\" | \"es\" | \"zh\",\n  PY: \"es\" | \"en\",\n  QA: \"en\" | \"fr\" | \"es\" | \"zh\" | \"ar\",\n  RE: \"en\" | \"fr\" | \"es\" | \"zh\",\n  RO: \"ro\" | \"en\",\n  RS: \"en\" | \"fr\" | \"es\" | \"zh\",\n  RU: \"ru\" | \"en\",\n  RW: \"fr\" | \"en\" | \"es\" | \"zh\",\n  SA: \"ar\" | \"en\" | \"fr\" | \"es\" | \"zh\",\n  SB: \"en\" | \"fr\" | \"es\" | \"zh\",\n  SC: \"fr\" | \"en\" | \"es\" | \"zh\",\n  SE: \"sv\" | \"en\",\n  SG: \"en\",\n  SH: \"en\" | \"fr\" | \"es\" | \"zh\",\n  SI: \"sl\" | \"en\",\n  SJ: \"en\" | \"fr\" | \"es\" | \"zh\",\n  SK: \"sk\" | \"en\",\n  SL: \"en\" | \"fr\" | \"es\" | \"zh\",\n  SM: \"en\" | \"fr\" | \"es\" | \"zh\",\n  SN: \"fr\" | \"en\" | \"es\" | \"zh\",\n  SO: \"en\" | \"fr\" | \"es\" | \"zh\",\n  SR: \"en\" | \"fr\" | \"es\" | \"zh\",\n  ST: \"en\" | \"fr\" | \"es\" | \"zh\",\n  SV: \"es\" | \"en\" | \"fr\" | \"zh\",\n  SZ: \"en\" | \"fr\" | \"es\" | \"zh\",\n  TC: \"en\" | \"fr\" | \"es\" | \"zh\",\n  TD: \"fr\" | \"en\" | \"es\" | \"zh\",\n  TG: \"fr\" | \"en\" | \"es\" | \"zh\",\n  TH: \"th\" | \"en\",\n  TJ: \"en\" | \"fr\" | \"es\" | \"zh\",\n  TM: \"en\" | \"fr\" | \"es\" | \"zh\",\n  TN: \"ar\" | \"en\" | \"fr\" | \"es\" | \"zh\",\n  TO: \"en\",\n  TR: \"tr\" | \"en\",\n  TT: \"en\" | \"fr\" | \"es\" | \"zh\",\n  TV: \"en\" | \"fr\" | \"es\" | \"zh\",\n  TW: \"zh_Hant\" | \"en\",\n  TZ: \"en\" | \"fr\" | \"es\" | \"zh\",\n  UA: \"en\" | \"ru\" | \"fr\" | \"es\" | \"zh\",\n  UG: \"en\" | \"fr\" | \"es\" | \"zh\",\n  US: \"en\" | \"fr\" | \"es\" | \"zh\",\n  UY: \"es\" | \"en\" | \"fr\" | \"zh\",\n  VA: \"en\" | \"fr\" | \"es\" | \"zh\",\n  VC: \"en\" | \"fr\" | \"es\" | \"zh\",\n  VE: \"es\" | \"en\" | \"fr\" | \"zh\",\n  VG: \"en\" | \"fr\" | \"es\" | \"zh\",\n  VN: \"vi\" | \"en\",\n  VU: \"en\" | \"fr\" | \"es\" | \"zh\",\n  WF: \"en\" | \"fr\" | \"es\" | \"zh\",\n  WS: \"en\",\n  YE: \"ar\" | \"en\" | \"fr\" | \"es\" | \"zh\",\n  YT: \"en\" | \"fr\" | \"es\" | \"zh\",\n  ZA: \"en\" | \"fr\" | \"es\" | \"zh\",\n  ZM: \"en\" | \"fr\" | \"es\" | \"zh\",\n  ZW: \"en\",\n|};\n\ntype CountryMap = {\n  [$Values<typeof COUNTRY>]: $ReadOnlyArray<$Values<typeof LANG>>,\n};\n\nexport const COUNTRY_LANGS: CountryMap = {\n  AD: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  AE: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH, LANG.AR],\n  AG: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  AI: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  AL: [LANG.SQ, LANG.EN],\n  AM: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  AN: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  AO: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  AR: [LANG.ES, LANG.EN],\n  AT: [LANG.DE, LANG.EN],\n  AU: [LANG.EN],\n  AW: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  AZ: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  BA: [LANG.EN],\n  BB: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  BE: [LANG.EN, LANG.NL, LANG.FR],\n  BF: [LANG.FR, LANG.EN, LANG.ES, LANG.ZH],\n  BG: [LANG.BG, LANG.EN],\n  BH: [LANG.AR, LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  BI: [LANG.FR, LANG.EN, LANG.ES, LANG.ZH],\n  BJ: [LANG.FR, LANG.EN, LANG.ES, LANG.ZH],\n  BM: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  BN: [LANG.MS, LANG.EN],\n  BO: [LANG.ES, LANG.EN, LANG.FR, LANG.ZH],\n  BR: [LANG.PT, LANG.EN],\n  BS: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  BT: [LANG.EN],\n  BW: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  BY: [LANG.EN],\n  BZ: [LANG.EN, LANG.ES, LANG.FR, LANG.ZH],\n  CA: [LANG.EN, LANG.FR],\n  CD: [LANG.FR, LANG.EN, LANG.ES, LANG.ZH],\n  CG: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  CH: [LANG.DE, LANG.FR, LANG.EN],\n  CI: [LANG.FR, LANG.EN],\n  CK: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  CL: [LANG.ES, LANG.EN, LANG.FR, LANG.ZH],\n  CM: [LANG.FR, LANG.EN],\n  CN: [LANG.ZH],\n  CO: [LANG.ES, LANG.EN, LANG.FR, LANG.ZH],\n  CR: [LANG.ES, LANG.EN, LANG.FR, LANG.ZH],\n  CV: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  CY: [LANG.EN],\n  CZ: [LANG.CS, LANG.EN],\n  DE: [LANG.DE, LANG.EN],\n  DJ: [LANG.FR, LANG.EN, LANG.ES, LANG.ZH],\n  DK: [LANG.DA, LANG.EN],\n  DM: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  DO: [LANG.ES, LANG.EN, LANG.FR, LANG.ZH],\n  DZ: [LANG.AR, LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  EC: [LANG.ES, LANG.EN, LANG.FR, LANG.ZH],\n  EE: [LANG.ET, LANG.EN, LANG.RU],\n  EG: [LANG.AR, LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  ER: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  ES: [LANG.ES, LANG.EN],\n  ET: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  FI: [LANG.FI, LANG.EN],\n  FJ: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  FK: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  FM: [LANG.EN],\n  FO: [LANG.DA, LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  FR: [LANG.FR, LANG.EN],\n  GA: [LANG.FR, LANG.EN, LANG.ES, LANG.ZH],\n  GB: [LANG.EN],\n  GD: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  GE: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  GF: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  GI: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  GL: [LANG.DA, LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  GM: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  GN: [LANG.FR, LANG.EN, LANG.ES, LANG.ZH],\n  GP: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  GR: [LANG.EL, LANG.EN],\n  GT: [LANG.ES, LANG.EN, LANG.FR, LANG.ZH],\n  GW: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  GY: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  HK: [LANG.EN, LANG.ZH_HANT, LANG.ZH],\n  HN: [LANG.ES, LANG.EN, LANG.FR, LANG.ZH],\n  HR: [LANG.EN],\n  HU: [LANG.HU, LANG.EN],\n  ID: [LANG.ID, LANG.EN],\n  IE: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  IL: [LANG.HE, LANG.EN],\n  IN: [LANG.EN],\n  IS: [LANG.EN],\n  IT: [LANG.IT, LANG.EN],\n  JM: [LANG.EN, LANG.ES, LANG.FR, LANG.ZH],\n  JO: [LANG.AR, LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  JP: [LANG.JA, LANG.EN],\n  KE: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  KG: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  KH: [LANG.EN],\n  KI: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  KM: [LANG.FR, LANG.EN, LANG.ES, LANG.ZH],\n  KN: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  KR: [LANG.KO, LANG.EN],\n  KW: [LANG.AR, LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  KY: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  KZ: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  LA: [LANG.EN],\n  LC: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  LI: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  LK: [LANG.SI, LANG.EN],\n  LS: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  LT: [LANG.LT, LANG.EN, LANG.RU, LANG.ZH],\n  LU: [LANG.EN, LANG.DE, LANG.FR, LANG.ES, LANG.ZH],\n  LV: [LANG.LV, LANG.EN, LANG.RU],\n  MA: [LANG.AR, LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  MC: [LANG.FR, LANG.EN],\n  MD: [LANG.EN],\n  ME: [LANG.EN],\n  MG: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  MH: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  MK: [LANG.EN],\n  ML: [LANG.FR, LANG.EN, LANG.ES, LANG.ZH],\n  MN: [LANG.EN],\n  MQ: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  MR: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  MS: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  MT: [LANG.EN],\n  MU: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  MV: [LANG.EN],\n  MW: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  MX: [LANG.ES, LANG.EN],\n  MY: [LANG.MS, LANG.EN],\n  MZ: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  NA: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  NC: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  NE: [LANG.FR, LANG.EN, LANG.ES, LANG.ZH],\n  NF: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  NG: [LANG.EN],\n  NI: [LANG.ES, LANG.EN, LANG.FR, LANG.ZH],\n  NL: [LANG.NL, LANG.EN],\n  NO: [LANG.NO, LANG.EN],\n  NP: [LANG.EN],\n  NR: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  NU: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  NZ: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  OM: [LANG.AR, LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  PA: [LANG.ES, LANG.EN, LANG.FR, LANG.ZH],\n  PE: [LANG.ES, LANG.EN, LANG.FR, LANG.ZH],\n  PF: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  PG: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  PH: [LANG.TL, LANG.EN],\n  PL: [LANG.PL, LANG.EN],\n  PM: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  PN: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  PT: [LANG.PT, LANG.EN],\n  PW: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  PY: [LANG.ES, LANG.EN],\n  QA: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH, LANG.AR],\n  RE: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  RO: [LANG.RO, LANG.EN],\n  RS: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  RU: [LANG.RU, LANG.EN],\n  RW: [LANG.FR, LANG.EN, LANG.ES, LANG.ZH],\n  SA: [LANG.AR, LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  SB: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  SC: [LANG.FR, LANG.EN, LANG.ES, LANG.ZH],\n  SE: [LANG.SV, LANG.EN],\n  SG: [LANG.EN],\n  SH: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  SI: [LANG.SL, LANG.EN],\n  SJ: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  SK: [LANG.SK, LANG.EN],\n  SL: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  SM: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  SN: [LANG.FR, LANG.EN, LANG.ES, LANG.ZH],\n  SO: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  SR: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  ST: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  SV: [LANG.ES, LANG.EN, LANG.FR, LANG.ZH],\n  SZ: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  TC: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  TD: [LANG.FR, LANG.EN, LANG.ES, LANG.ZH],\n  TG: [LANG.FR, LANG.EN, LANG.ES, LANG.ZH],\n  TH: [LANG.TH, LANG.EN],\n  TJ: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  TM: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  TN: [LANG.AR, LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  TO: [LANG.EN],\n  TR: [LANG.TR, LANG.EN],\n  TT: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  TV: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  TW: [LANG.ZH_HANT, LANG.ZH, LANG.EN],\n  TZ: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  UA: [LANG.EN, LANG.RU, LANG.FR, LANG.ES, LANG.ZH],\n  UG: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  US: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  UY: [LANG.ES, LANG.EN, LANG.FR, LANG.ZH],\n  VA: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  VC: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  VE: [LANG.ES, LANG.EN, LANG.FR, LANG.ZH],\n  VG: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  VN: [LANG.VI, LANG.EN],\n  VU: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  WF: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  WS: [LANG.EN],\n  YE: [LANG.AR, LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  YT: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  ZA: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  ZM: [LANG.EN, LANG.FR, LANG.ES, LANG.ZH],\n  ZW: [LANG.EN],\n};\n\nexport type LocaleType = {|\n  country: $Values<typeof COUNTRY>,\n  lang: $Values<typeof LANG>,\n|};\n", "/* @flow */\n\nexport const INTENT = {\n  CAPTURE: (\"capture\": \"capture\"),\n  AUTHORIZE: (\"authorize\": \"authorize\"),\n  ORDER: (\"order\": \"order\"),\n  TOKENIZE: (\"tokenize\": \"tokenize\"),\n  SUBSCRIPTION: (\"subscription\": \"subscription\"),\n};\n\nexport const COMMIT = {\n  TRUE: (true: true),\n  FALSE: (false: false),\n};\n\nexport const VAULT = {\n  TRUE: (true: true),\n  FALSE: (false: false),\n};\n\nexport const CURRENCY = {\n  AED: (\"AED\": \"AED\"),\n  AFN: (\"AFN\": \"AFN\"),\n  ALL: (\"ALL\": \"ALL\"),\n  AMD: (\"AMD\": \"AMD\"),\n  ANG: (\"ANG\": \"ANG\"),\n  ARS: (\"ARS\": \"ARS\"),\n  AOA: (\"AOA\": \"AOA\"),\n  AUD: (\"AUD\": \"AUD\"),\n  AWG: (\"AWG\": \"AWG\"),\n  AZN: (\"AZN\": \"AZN\"),\n  BAM: (\"BAM\": \"BAM\"),\n  BBD: (\"BBD\": \"BBD\"),\n  BDT: (\"BDT\": \"BDT\"),\n  BGN: (\"BGN\": \"BGN\"),\n  BHD: (\"BHD\": \"BHD\"),\n  BIF: (\"BIF\": \"BIF\"),\n  BMD: (\"BMD\": \"BMD\"),\n  BND: (\"BND\": \"BND\"),\n  BOB: (\"BOB\": \"BOB\"),\n  BRL: (\"BRL\": \"BRL\"),\n  BSD: (\"BSD\": \"BSD\"),\n  BTN: (\"BTN\": \"BTN\"),\n  BWP: (\"BWP\": \"BWP\"),\n  BZD: (\"BZD\": \"BZD\"),\n  CAD: (\"CAD\": \"CAD\"),\n  CDF: (\"CDF\": \"CDF\"),\n  CHF: (\"CHF\": \"CHF\"),\n  CLP: (\"CLP\": \"CLP\"),\n  COP: (\"COP\": \"COP\"),\n  CRC: (\"CRC\": \"CRC\"),\n  CVE: (\"CVE\": \"CVE\"),\n  CZK: (\"CZK\": \"CZK\"),\n  DJF: (\"DJF\": \"DJF\"),\n  DKK: (\"DKK\": \"DKK\"),\n  DOP: (\"DOP\": \"DOP\"),\n  DZD: (\"DZD\": \"DZD\"),\n  EGP: (\"EGP\": \"EGP\"),\n  ETB: (\"ETB\": \"ETB\"),\n  ERN: (\"ERN\": \"ERN\"),\n  EUR: (\"EUR\": \"EUR\"),\n  FJD: (\"FJD\": \"FJD\"),\n  FKP: (\"FKP\": \"FKP\"),\n  GBP: (\"GBP\": \"GBP\"),\n  GEL: (\"GEL\": \"GEL\"),\n  GHS: (\"GHS\": \"GHS\"),\n  GIP: (\"GIP\": \"GIP\"),\n  GMD: (\"GMD\": \"GMD\"),\n  GNF: (\"GNF\": \"GNF\"),\n  GTQ: (\"GTQ\": \"GTQ\"),\n  GYD: (\"GYD\": \"GYD\"),\n  HKD: (\"HKD\": \"HKD\"),\n  HNL: (\"HNL\": \"HNL\"),\n  HRK: (\"HRK\": \"HRK\"),\n  HTG: (\"HTG\": \"HTG\"),\n  HUF: (\"HUF\": \"HUF\"),\n  IDR: (\"IDR\": \"IDR\"),\n  ILS: (\"ILS\": \"ILS\"),\n  INR: (\"INR\": \"INR\"),\n  ISK: (\"ISK\": \"ISK\"),\n  JMD: (\"JMD\": \"JMD\"),\n  JOD: (\"JOD\": \"JOD\"),\n  JPY: (\"JPY\": \"JPY\"),\n  KGS: (\"KGS\": \"KGS\"),\n  KES: (\"KES\": \"KES\"),\n  KHR: (\"KHR\": \"KHR\"),\n  KMF: (\"KMF\": \"KMF\"),\n  KRW: (\"KRW\": \"KRW\"),\n  KWD: (\"KWD\": \"KWD\"),\n  KYD: (\"KYD\": \"KYD\"),\n  KZT: (\"KZT\": \"KZT\"),\n  LAK: (\"LAK\": \"LAK\"),\n  LKR: (\"LKR\": \"LKR\"),\n  LRD: (\"LRD\": \"LRD\"),\n  LSL: (\"LSL\": \"LSL\"),\n  MAD: (\"MAD\": \"MAD\"),\n  MDL: (\"MDL\": \"MDL\"),\n  MGA: (\"MGA\": \"MGA\"),\n  MKD: (\"MKD\": \"MKD\"),\n  MNT: (\"MNT\": \"MNT\"),\n  MOP: (\"MOP\": \"MOP\"),\n  MRO: (\"MRO\": \"MRO\"),\n  MRU: (\"MRU\": \"MRU\"),\n  MUR: (\"MUR\": \"MUR\"),\n  MVR: (\"MVR\": \"MVR\"),\n  MWK: (\"MWK\": \"MWK\"),\n  MXN: (\"MXN\": \"MXN\"),\n  MYR: (\"MYR\": \"MYR\"),\n  MZN: (\"MZN\": \"MZN\"),\n  NAD: (\"NAD\": \"NAD\"),\n  NGN: (\"NGN\": \"NGN\"),\n  NIO: (\"NIO\": \"NIO\"),\n  NOK: (\"NOK\": \"NOK\"),\n  NPR: (\"NPR\": \"NPR\"),\n  NZD: (\"NZD\": \"NZD\"),\n  OMR: (\"OMR\": \"OMR\"),\n  PAB: (\"PAB\": \"PAB\"),\n  PEN: (\"PEN\": \"PEN\"),\n  PGK: (\"PGK\": \"PGK\"),\n  PHP: (\"PHP\": \"PHP\"),\n  PKR: (\"PKR\": \"PKR\"),\n  PLN: (\"PLN\": \"PLN\"),\n  PYG: (\"PYG\": \"PYG\"),\n  QAR: (\"QAR\": \"QAR\"),\n  RON: (\"RON\": \"RON\"),\n  RSD: (\"RSD\": \"RSD\"),\n  RUB: (\"RUB\": \"RUB\"),\n  RWF: (\"RWF\": \"RWF\"),\n  SAR: (\"SAR\": \"SAR\"),\n  SBD: (\"SBD\": \"SBD\"),\n  SCR: (\"SCR\": \"SCR\"),\n  SEK: (\"SEK\": \"SEK\"),\n  SGD: (\"SGD\": \"SGD\"),\n  SHP: (\"SHP\": \"SHP\"),\n  SLE: (\"SLE\": \"SLE\"),\n  SLL: (\"SLL\": \"SLL\"),\n  SOS: (\"SOS\": \"SOS\"),\n  SRD: (\"SRD\": \"SRD\"),\n  STN: (\"STN\": \"STN\"),\n  SVC: (\"SVC\": \"SVC\"),\n  SZL: (\"SZL\": \"SZL\"),\n  THB: (\"THB\": \"THB\"),\n  TMT: (\"TMT\": \"TMT\"),\n  TJS: (\"TJS\": \"TJS\"),\n  TND: (\"TND\": \"TND\"),\n  TOP: (\"TOP\": \"TOP\"),\n  TTD: (\"TTD\": \"TTD\"),\n  TWD: (\"TWD\": \"TWD\"),\n  TZS: (\"TZS\": \"TZS\"),\n  UAH: (\"UAH\": \"UAH\"),\n  UGX: (\"UGX\": \"UGX\"),\n  USD: (\"USD\": \"USD\"),\n  UYU: (\"UYU\": \"UYU\"),\n  UZS: (\"UZS\": \"UZS\"),\n  VES: (\"VES\": \"VES\"),\n  VND: (\"VND\": \"VND\"),\n  VUV: (\"VUV\": \"VUV\"),\n  WST: (\"WST\": \"WST\"),\n  XAF: (\"XAF\": \"XAF\"),\n  XCD: (\"XCD\": \"XCD\"),\n  XOF: (\"XOF\": \"XOF\"),\n  XPF: (\"XPF\": \"XPF\"),\n  YER: (\"YER\": \"YER\"),\n  ZAR: (\"ZAR\": \"ZAR\"),\n  ZMW: (\"ZMW\": \"ZMW\"),\n};\n", "/* @flow */\n\nexport const SDK_PATH = (\"/sdk/js\": \"/sdk/js\");\nexport const WEB_SDK_BRIDGE_PATH = (\"/web-sdk/v6/bridge\": \"/web-sdk/v6/bridge\");\n\nexport const SDK_SETTINGS = {\n  AMOUNT: (\"data-amount\": \"data-amount\"),\n  API_STAGE_HOST: (\"data-api-stage-host\": \"data-api-stage-host\"),\n  CLIENT_METADATA_ID: (\"data-client-metadata-id\": \"data-client-metadata-id\"),\n  CLIENT_TOKEN: (\"data-client-token\": \"data-client-token\"),\n  CSP_NONCE: (\"data-csp-nonce\": \"data-csp-nonce\"),\n  ENABLE_3DS: (\"data-enable-3ds\": \"data-enable-3ds\"),\n  JS_SDK_LIBRARY: (\"data-js-sdk-library\": \"data-js-sdk-library\"),\n  MERCHANT_ID: (\"data-merchant-id\": \"data-merchant-id\"),\n  NAMESPACE: (\"data-namespace\": \"data-namespace\"),\n  PAGE_TYPE: (\"data-page-type\": \"data-page-type\"),\n  PARTNER_ATTRIBUTION_ID:\n    (\"data-partner-attribution-id\": \"data-partner-attribution-id\"),\n  POPUPS_DISABLED: (\"data-popups-disabled\": \"data-popups-disabled\"),\n  SDK_INTEGRATION_SOURCE:\n    (\"data-sdk-integration-source\": \"data-sdk-integration-source\"),\n  SDK_TOKEN: (\"data-sdk-client-token\": \"data-sdk-client-token\"),\n  SHOPPER_SESSION_ID: (\"data-shopper-session-id\": \"data-shopper-session-id\"),\n  STAGE_HOST: (\"data-stage-host\": \"data-stage-host\"),\n  USER_EXPERIENCE_FLOW:\n    (\"data-user-experience-flow\": \"data-user-experience-flow\"),\n  USER_ID_TOKEN: (\"data-user-id-token\": \"data-user-id-token\"),\n};\n\n// Why do we call these settings instead of what they are, data attributes?\n// all other constants in this file are named after what they are\nexport const SDK_DATA_ATTRIBUTES = SDK_SETTINGS;\n\nexport const SDK_QUERY_KEYS = {\n  COMPONENTS: (\"components\": \"components\"),\n\n  ENV: (\"env\": \"env\"),\n  DEBUG: (\"debug\": \"debug\"),\n  CACHEBUST: (\"cachebust\": \"cachebust\"),\n\n  CLIENT_ID: (\"client-id\": \"client-id\"),\n  MERCHANT_ID: (\"merchant-id\": \"merchant-id\"),\n\n  LOCALE: (\"locale\": \"locale\"),\n\n  CURRENCY: (\"currency\": \"currency\"),\n  INTENT: (\"intent\": \"intent\"),\n  COMMIT: (\"commit\": \"commit\"),\n  VAULT: (\"vault\": \"vault\"),\n\n  BUYER_COUNTRY: (\"buyer-country\": \"buyer-country\"),\n\n  ENABLE_FUNDING: (\"enable-funding\": \"enable-funding\"),\n  DISABLE_FUNDING: (\"disable-funding\": \"disable-funding\"),\n  DISABLE_CARD: (\"disable-card\": \"disable-card\"),\n\n  INTEGRATION_DATE: (\"integration-date\": \"integration-date\"),\n  STAGE_HOST: (\"stage-host\": \"stage-host\"),\n  STAGE_ALIAS: (\"stage-alias\": \"stage-alias\"),\n  CDN_REGISTRY: (\"cdn-registry\": \"cdn-registry\"),\n  VERSION: (\"version\": \"version\"),\n};\n\nexport const COMPONENTS = {\n  BUTTONS: (\"buttons\": \"buttons\"),\n  CARD_FIELDS: (\"card-fields\": \"card-fields\"),\n  HOSTED_BUTTONS: (\"hosted-buttons\": \"hosted-buttons\"),\n  HOSTED_FIELDS: (\"hosted-fields\": \"hosted-fields\"),\n};\n\nexport const DEBUG = {\n  TRUE: (true: true),\n  FALSE: (false: false),\n};\n\nexport const QUERY_BOOL = {\n  TRUE: (\"true\": \"true\"),\n  FALSE: (\"false\": \"false\"),\n};\n\nexport const UNKNOWN = (\"unknown\": \"unknown\");\n\nexport const PROTOCOL = {\n  HTTP: (\"http\": \"http\"),\n  HTTPS: (\"https\": \"https\"),\n};\n\nexport const PAGE_TYPES = {\n  HOME: (\"home\": \"home\"),\n  PRODUCT: (\"product\": \"product\"),\n  CART: (\"cart\": \"cart\"),\n  CHECKOUT: (\"checkout\": \"checkout\"),\n  PRODUCT_LISTING: (\"product-listing\": \"product-listing\"),\n  SEARCH_RESULTS: (\"search-results\": \"search-results\"),\n  PRODUCT_DETAILS: (\"product-details\": \"product-details\"),\n  MINI_CART: (\"mini-cart\": \"mini-cart\"),\n};\n\nexport const MERCHANT_ID_MAX = 10;\n\nexport const DISPLAY_ONLY_VALUES = {\n  VAULTABLE: (\"vaultable\": \"vaultable\"),\n};\n", "/* @flow */\n\nimport { COUNTRY } from \"./locale\";\nimport { CURRENCY, INTENT, COMMIT, VAULT } from \"./order\";\nimport { COMPONENTS, DEBUG } from \"./params\";\n\nexport const DEFAULT_COUNTRY = COUNTRY.US;\nexport const DEFAULT_CURRENCY = CURRENCY.USD;\nexport const DEFAULT_INTENT = INTENT.CAPTURE;\nexport const DEFAULT_COMMIT = COMMIT.TRUE;\nexport const DEFAULT_SALE_COMMIT = COMMIT.TRUE;\nexport const DEFAULT_NONSALE_COMMIT = COMMIT.TRUE;\nexport const DEFAULT_VAULT = VAULT.FALSE;\nexport const DEFAULT_COMPONENTS = COMPONENTS.BUTTONS;\nexport const DEFAULT_DEBUG = DEBUG.FALSE;\n", "/* @flow */\n\nexport const ENV = {\n  LOCAL: (\"local\": \"local\"),\n  STAGE: (\"stage\": \"stage\"),\n  SANDBOX: (\"sandbox\": \"sandbox\"),\n  PRODUCTION: (\"production\": \"production\"),\n  TEST: (\"test\": \"test\"),\n};\n\nexport const MOBILE_ENV = {\n  ANDROID: (\"android\": \"android\"),\n  IOS: (\"iOS\": \"iOS\"),\n};\n", "/* @flow */\n\nexport const ERROR_CODE = {\n  VALIDATION_ERROR: (\"validation_error\": \"validation_error\"),\n};\n", "/* @flow */\n\nexport const FPTI_KEY = {\n  BUTTON_LAYOUT: (\"button_layout\": \"button_layout\"),\n  BUTTON_MESSAGE_AMOUNT: (\"button_message_amount\": \"button_message_amount\"),\n  BUTTON_MESSAGE_CREDIT_PRODUCT_IDENTIFIER:\n    (\"button_message_credit_product_identifier\": \"button_message_credit_product_identifier\"),\n  BUTTON_MESSAGE_COLOR: (\"button_message_color\": \"button_message_color\"),\n  BUTTON_MESSAGE_CURRENCY:\n    (\"button_message_currency\": \"button_message_currency\"),\n  BUTTON_MESSAGE_ALIGN: (\"button_message_align\": \"button_message_align\"),\n  BUTTON_MESSAGE_POSITION:\n    (\"button_message_position\": \"button_message_position\"),\n  BUTTON_MESSAGE_OFFER_COUNTRY:\n    (\"button_message_offer_country\": \"button_message_offer_country\"),\n  BUTTON_MESSAGE_OFFER_TYPE:\n    (\"button_message_offer_type\": \"button_message_offer_type\"),\n  BUTTON_MESSAGE_TYPE: (\"button_message_type\": \"button_message_type\"),\n  BUTTON_SESSION_UID: (\"button_session_id\": \"button_session_id\"),\n  BUTTON_SOURCE: (\"button_source\": \"button_source\"),\n  BUTTON_TYPE: (\"button_type\": \"button_type\"),\n  BUTTON_VERSION: (\"button_version\": \"button_version\"),\n  BUYER_COUNTRY: (\"buyer_cntry\": \"buyer_cntry\"),\n  CHECKOUT_APP: (\"checkout_app\": \"checkout_app\"),\n  CHOSEN_FI_TYPE: (\"chosen_fi_type\": \"chosen_fi_type\"),\n  CHOSEN_FI_ID: (\"chosen_fi_id\": \"chosen_fi_id\"),\n  CHOSEN_FUNDING: (\"selected_payment_method\": \"selected_payment_method\"),\n  CLIENT_ID: (\"client_id\": \"client_id\"),\n  CONTEXT_CORRID: (\"context_correlation_id\": \"context_correlation_id\"),\n  CONTEXT_ID: (\"context_id\": \"context_id\"),\n  CONTEXT_TYPE: (\"context_type\": \"context_type\"),\n  CPL_CHUNK_METRICS: (\"cpl_chunk_metrics\": \"cpl_chunk_metrics\"),\n  CPL_COMP_METRICS: (\"cpl_comp_metrics\": \"cpl_comp_metrics\"),\n  CPL_QUERY_METRICS: (\"cpl_query_metrics\": \"cpl_query_metrics\"),\n  DATA_SOURCE: (\"serverside_data_source\": \"serverside_data_source\"),\n  DISABLE_CARD: (\"disable_card\": \"disable_card\"),\n  DISABLE_FUNDING: (\"disable_funding\": \"disable_funding\"),\n  ERROR_CODE: (\"ext_error_code\": \"ext_error_code\"),\n  ERROR_DESC: (\"ext_error_desc\": \"ext_error_desc\"),\n  EVENT_NAME: (\"event_name\": \"event_name\"),\n  EXPERIMENT_EXPERIENCE:\n    (\"experimentation_experience\": \"experimentation_experience\"),\n  EXPERIMENT_NAME: (\"pxp_exp_id\": \"pxp_exp_id\"),\n  EXPERIMENT_TREATMENT:\n    (\"experimentation_treatment\": \"experimentation_treatment\"),\n  FEED: (\"feed_name\": \"feed_name\"),\n  FI_ID: (\"fi_id\": \"fi_id\"),\n  FI_LIST: (\"fi_list\": \"fi_list\"),\n  FIELDS_COMPONENT_SESSION_ID:\n    (\"fields_component_session_id\": \"fields_component_session_id\"),\n  FLOW: (\"flow\": \"flow\"),\n  FUNDING_COUNT: (\"eligible_payment_count\": \"eligible_payment_count\"),\n  FUNDING_LIST: (\"eligible_payment_methods\": \"eligible_payment_methods\"),\n  HOSTED_BUTTON_ID: (\"hosted_button_id\": \"hosted_button_id\"),\n  INTEGRATION_IDENTIFIER: (\"integration_identifier\": \"integration_identifier\"),\n  IS_VAULT: (\"is_vault\": \"is_vault\"),\n  JS_SDK_LIBRARY: (\"js_sdk_library\": \"js_sdk_library\"),\n  LOCALE: (\"locale\": \"locale\"),\n  MERCHANT_DOMAIN: (\"merchant_domain\": \"merchant_domain\"),\n  MOBILE_APP_VERSION: (\"mobile_app_version\": \"mobile_app_version\"),\n  MOBILE_BUNDLE_IDENTIFIER: (\"mapv\": \"mapv\"),\n  OPTION_SELECTED: (\"optsel\": \"optsel\"),\n  PAGE: (\"page_name\": \"page_name\"),\n  PAGE_LOAD_TIME: (\"page_load_time\": \"page_load_time\"),\n  PAGE_TYPE: (\"pp_placement\": \"pp_placement\"),\n  PARTNER_ATTRIBUTION_ID: (\"bn_code\": \"bn_code\"),\n  PAY_ID: (\"pay_id\": \"pay_id\"),\n  PAY_NOW: (\"pay_now\": \"pay_now\"),\n  PAYMENT_FLOW: (\"payment_flow\": \"payment_flow\"),\n  POTENTIAL_PAYMENT_METHODS:\n    (\"potential_payment_methods\": \"potential_payment_methods\"),\n  PRODUCT: (\"product\": \"product\"),\n  RECOMMENDED_PAYMENT: (\"recommended_payment\": \"recommended_payment\"),\n  REFERER: (\"referer_url\": \"referer_url\"),\n  REFERRER_DOMAIN: (\"referrer_domain\", \"referrer_domain\"),\n  RESPONSE_DURATION: (\"response_duration\": \"response_duration\"),\n  SDK_CACHE: (\"sdk_cache\": \"sdk_cache\"),\n  SDK_ENVIRONMENT: (\"sdk_environment\": \"sdk_environment\"),\n  SDK_INTEGRATION_SOURCE: (\"sdk_integration_source\": \"sdk_integration_source\"),\n  SDK_LOAD_TIME: (\"sdk_load_time\": \"sdk_load_time\"),\n  SDK_NAME: (\"sdk_name\": \"sdk_name\"),\n  SDK_VERSION: (\"sdk_version\": \"sdk_version\"),\n  SELECTED_FI:\n    (\"merchant_selected_funding_source\": \"merchant_selected_funding_source\"),\n  SELLER_ID: (\"seller_id\": \"seller_id\"),\n  SESSION_UID: (\"page_session_id\": \"page_session_id\"),\n  SMART_WALLET_INSTRUMENT_TYPES:\n    (\"smart_wallet_instrument_types\": \"smart_wallet_instrument_types\"),\n  SPACE_KEY: (\"space_key\": \"space_key\"),\n  STATE: (\"state_name\": \"state_name\"),\n  STICKINESS_ID: (\"stickiness_id\": \"stickiness_id\"),\n  TIMESTAMP: (\"t\": \"t\"),\n  TOKEN: (\"token\": \"token\"),\n  TRANSITION: (\"transition_name\": \"transition_name\"),\n  TRANSITION_TIME: (\"transition_time\": \"transition_time\"),\n  TREATMENT_NAME: (\"pxp_trtmnt_id\": \"pxp_trtmnt_id\"),\n  USER_ACTION: (\"user_action\": \"user_action\"),\n  USER_AGENT: (\"user_agent\": \"user_agent\"),\n  USER_IDENTITY_METHOD: (\"user_identity_method\": \"user_identity_method\"),\n  VERSION: (\"checkoutjs_version\": \"checkoutjs_version\"),\n  AVAILABLE_PAYMENT_NETWORKS:\n    (\"available_payment_networks\": \"available_payment_networks\"),\n  SELECTED_CARD_TYPE: (\"selected_card_type\": \"selected_card_type\"),\n  CURRENCY: (\"currency\": \"currency\"),\n  AMOUNT: (\"amount\": \"amount\"),\n};\n\nexport const FPTI_USER_ACTION = {\n  COMMIT: (\"commit\": \"commit\"),\n  CONTINUE: (\"continue\": \"continue\"),\n};\n\nexport const FPTI_DATA_SOURCE = {\n  PAYMENTS_SDK: (\"checkout\": \"checkout\"),\n};\n\nexport const FPTI_FEED = {\n  PAYMENTS_SDK: (\"payments_sdk\": \"payments_sdk\"),\n};\n\nexport const FPTI_SDK_NAME = {\n  PAYMENTS_SDK: (\"payments_sdk\": \"payments_sdk\"),\n};\n", "/* @flow */\n\nexport const BASE_SDK_METRIC_NAMESPACE = \"pp.sdks.ppcp\";\n\nexport const payPalWebV5Dimensions = {\n  sdk_platform: \"web\",\n  major_version: \"5\",\n};\n", "/* @flow */\n\nexport const PLATFORM = {\n  DESKTOP: (\"desktop\": \"desktop\"),\n  MOBILE: (\"mobile\": \"mobile\"),\n};\n", "/* @flow */\n\nexport const TYPES = true;\n\nexport type VaultedInstrument = {|\n  id: string,\n  label: {|\n    description: string,\n  |},\n|};\n\nexport type BasicEligibility = {|\n  eligible: ?boolean,\n  branded: ?boolean,\n  vaultable?: ?boolean,\n  recommended?: ?boolean,\n|};\n\nexport type PayPalEligibility = {|\n  eligible: ?boolean,\n  branded: ?boolean,\n  recommended?: ?boolean,\n  vaultable?: ?boolean,\n  vaultedInstruments?: $ReadOnlyArray<VaultedInstrument>,\n|};\n\nexport type FundingProductEligibility = {|\n  eligible?: boolean,\n  variant?: ?string,\n|};\n\nexport type PayLaterEligibility = {|\n  eligible: ?boolean,\n  recommended?: ?boolean,\n  vaultable?: ?boolean,\n  products?: {|\n    payIn3: FundingProductEligibility,\n    payIn4: FundingProductEligibility,\n    paylater: FundingProductEligibility,\n  |},\n|};\n\nexport type CardVendorEligibility = {|\n  eligible: ?boolean,\n  vaultable?: ?boolean,\n  vaultedInstruments?: $ReadOnlyArray<VaultedInstrument>,\n|};\n\nexport type CardVendorsEligibility = {|\n  visa?: CardVendorEligibility,\n  mastercard?: CardVendorEligibility,\n  amex?: CardVendorEligibility,\n  discover?: CardVendorEligibility,\n  hiper?: CardVendorEligibility,\n  elo?: CardVendorEligibility,\n  jcb?: CardVendorEligibility,\n  cup?: CardVendorEligibility,\n  maestro?: CardVendorEligibility,\n  diners?: CardVendorEligibility,\n  eftpos?: CardVendorEligibility,\n|};\n\nexport type CardEligibility = {|\n  eligible: ?boolean,\n  branded: ?boolean,\n  recommended?: ?boolean,\n  installments?: ?boolean,\n  guestEnabled?: ?boolean,\n  vendors: CardVendorsEligibility,\n|};\n\nexport type FundingEligibilityType = {|\n  paypal?: PayPalEligibility,\n  card?: CardEligibility,\n  venmo?: BasicEligibility,\n  applepay?: BasicEligibility,\n  credit?: BasicEligibility,\n  paylater?: PayLaterEligibility,\n  sepa?: BasicEligibility,\n  bancontact?: BasicEligibility,\n  eps?: BasicEligibility,\n  giropay?: BasicEligibility,\n  ideal?: BasicEligibility,\n  mybank?: BasicEligibility,\n  p24?: BasicEligibility,\n  sofort?: BasicEligibility,\n  wechatpay?: BasicEligibility,\n  itau?: BasicEligibility,\n  payu?: BasicEligibility,\n  blik?: BasicEligibility,\n  boleto?: BasicEligibility,\n  boletobancario?: BasicEligibility,\n  oxxo?: BasicEligibility,\n  trustly?: BasicEligibility,\n  mercadopago?: BasicEligibility,\n  multibanco?: BasicEligibility,\n  satispay?: BasicEligibility,\n  paidy?: BasicEligibility,\n  // deprecated APMs will be removed soon\n  zimpler?: BasicEligibility,\n  maxima?: BasicEligibility,\n|};\n"], "sourceRoot": ""}