/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Csrc%5Clib%5Ccart.ts&server=false!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Csrc%5Clib%5Ccart.ts&server=false! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(app-pages-browser)/./node_modules/next/dist/client/image-component.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(app-pages-browser)/./node_modules/next/dist/client/link.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/lib/cart.ts */ \"(app-pages-browser)/./src/lib/cart.ts\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz1EJTNBJTVDV2ViU2l0ZVN0dWZmJTVDcnVzdC1wbHVnaW4tbWFya2V0cGxhY2UlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2ltYWdlLWNvbXBvbmVudC5qcyZtb2R1bGVzPUQlM0ElNUNXZWJTaXRlU3R1ZmYlNUNydXN0LXBsdWdpbi1tYXJrZXRwbGFjZSU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDbGluay5qcyZtb2R1bGVzPUQlM0ElNUNXZWJTaXRlU3R1ZmYlNUNydXN0LXBsdWdpbi1tYXJrZXRwbGFjZSU1Q3NyYyU1Q2xpYiU1Q2NhcnQudHMmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBb0k7QUFDcEksOE1BQXlIO0FBQ3pIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/YjFiOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFdlYlNpdGVTdHVmZlxcXFxydXN0LXBsdWdpbi1tYXJrZXRwbGFjZVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxpbWFnZS1jb21wb25lbnQuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFdlYlNpdGVTdHVmZlxcXFxydXN0LXBsdWdpbi1tYXJrZXRwbGFjZVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxsaW5rLmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxXZWJTaXRlU3R1ZmZcXFxccnVzdC1wbHVnaW4tbWFya2V0cGxhY2VcXFxcc3JjXFxcXGxpYlxcXFxjYXJ0LnRzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=D%3A%5CWebSiteStuff%5Crust-plugin-marketplace%5Csrc%5Clib%5Ccart.ts&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/cart.ts":
/*!*************************!*\
  !*** ./src/lib/cart.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToCart: function() { return /* binding */ addToCart; },\n/* harmony export */   clearCart: function() { return /* binding */ clearCart; },\n/* harmony export */   getCart: function() { return /* binding */ getCart; },\n/* harmony export */   getCartItemCount: function() { return /* binding */ getCartItemCount; },\n/* harmony export */   getCartTotal: function() { return /* binding */ getCartTotal; },\n/* harmony export */   removeFromCart: function() { return /* binding */ removeFromCart; },\n/* harmony export */   updateCartItemQuantity: function() { return /* binding */ updateCartItemQuantity; }\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ getCart,addToCart,removeFromCart,updateCartItemQuantity,clearCart,getCartTotal,getCartItemCount auto */ const CART_STORAGE_KEY = \"rust-plugins-cart\";\nfunction getCart() {\n    if (false) {}\n    try {\n        const cartData = localStorage.getItem(CART_STORAGE_KEY);\n        return cartData ? JSON.parse(cartData) : [];\n    } catch (e) {\n        return [];\n    }\n}\nfunction addToCart(plugin) {\n    const cart = getCart();\n    const existingItem = cart.find((item)=>item.plugin.id === plugin.id);\n    if (existingItem) {\n        existingItem.quantity += 1;\n    } else {\n        cart.push({\n            plugin,\n            quantity: 1\n        });\n    }\n    saveCart(cart);\n}\nfunction removeFromCart(pluginId) {\n    const cart = getCart().filter((item)=>item.plugin.id !== pluginId);\n    saveCart(cart);\n}\nfunction updateCartItemQuantity(pluginId, quantity) {\n    const cart = getCart();\n    const item = cart.find((item)=>item.plugin.id === pluginId);\n    if (item) {\n        if (quantity <= 0) {\n            removeFromCart(pluginId);\n        } else {\n            item.quantity = quantity;\n            saveCart(cart);\n        }\n    }\n}\nfunction clearCart() {\n    saveCart([]);\n}\nfunction getCartTotal() {\n    return getCart().reduce((total, item)=>total + item.plugin.price * item.quantity, 0);\n}\nfunction getCartItemCount() {\n    return getCart().reduce((count, item)=>count + item.quantity, 0);\n}\nfunction saveCart(cart) {\n    if (true) {\n        localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(cart));\n        // Dispatch custom event for cart updates\n        window.dispatchEvent(new CustomEvent(\"cartUpdated\"));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/cart.ts\n"));

/***/ })

});