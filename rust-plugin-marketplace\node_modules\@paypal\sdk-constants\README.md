## paypal-sdk-constants

[![build status][build-badge]][build]
[![npm version][version-badge]][package]
[![apache license][license-badge]][license]

[build-badge]: https://img.shields.io/github/actions/workflow/status/paypal/paypal-sdk-constants/main.yml?branch=main&logo=github&style=flat-square
[build]: https://github.com/paypal/paypal-sdk-constants/actions?query=workflow%3Abuild
[version-badge]: https://img.shields.io/npm/v/@paypal/sdk-constants.svg?style=flat-square
[package]: https://www.npmjs.com/package/@paypal/sdk-constants
[license-badge]: https://img.shields.io/npm/l/@paypal/sdk-constants.svg?style=flat-square
[license]: https://github.com/paypal/paypal-sdk-constants/blob/master/LICENSE

Constants for paypal sdk.

## Quick Start

#### Getting Started

- Fork the module
- Run setup: `npm run setup`
- Start editing code in `./src` and writing tests in `./tests`
- `npm run build`

#### Building

```bash
npm run build
```

#### Publishing

- Publish your code: `npm run release` to add a patch
  - Or `npm run release:path`, `npm run release:minor`, `npm run release:major`
