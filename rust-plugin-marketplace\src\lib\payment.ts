import { loadStripe } from '@stripe/stripe-js';
import { CartItem } from './types';

// Initialize Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '');

export interface PaymentMethod {
  id: string;
  name: string;
  icon: string;
  description: string;
}

export const paymentMethods: PaymentMethod[] = [
  {
    id: 'stripe',
    name: 'Credit Card',
    icon: 'CreditCard',
    description: 'Pay securely with your credit or debit card'
  },
  {
    id: 'paypal',
    name: 'PayPal',
    icon: 'Wallet',
    description: 'Pay with your PayPal account'
  }
];

export interface CheckoutSession {
  id: string;
  url: string;
  payment_method: string;
}

// Stripe checkout
export async function createStripeCheckoutSession(items: CartItem[]): Promise<CheckoutSession> {
  try {
    const response = await fetch('/api/checkout/stripe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ items }),
    });

    if (!response.ok) {
      throw new Error('Failed to create checkout session');
    }

    const session = await response.json();
    return session;
  } catch (error) {
    console.error('Error creating Stripe checkout session:', error);
    throw error;
  }
}

// PayPal checkout
export async function createPayPalOrder(items: CartItem[]): Promise<string> {
  try {
    const response = await fetch('/api/checkout/paypal', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ items }),
    });

    if (!response.ok) {
      throw new Error('Failed to create PayPal order');
    }

    const { orderID } = await response.json();
    return orderID;
  } catch (error) {
    console.error('Error creating PayPal order:', error);
    throw error;
  }
}

export async function capturePayPalOrder(orderID: string): Promise<any> {
  try {
    const response = await fetch('/api/checkout/paypal/capture', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ orderID }),
    });

    if (!response.ok) {
      throw new Error('Failed to capture PayPal order');
    }

    return await response.json();
  } catch (error) {
    console.error('Error capturing PayPal order:', error);
    throw error;
  }
}

// Utility functions
export function calculateTotal(items: CartItem[]): number {
  return items.reduce((total, item) => total + (item.plugin.price * item.quantity), 0);
}

export function formatCurrency(amount: number): string {
  return (amount / 100).toFixed(2);
}
