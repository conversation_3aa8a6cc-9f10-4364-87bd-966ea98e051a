import { notFound } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import PluginCard from '@/components/PluginCard';
import { getCategoryById, getPluginsByCategory } from '@/lib/plugins';

interface CategoryPageProps {
  params: {
    id: string;
  };
}

export default function CategoryPage({ params }: CategoryPageProps) {
  const category = getCategoryById(params.id);
  const plugins = getPluginsByCategory(params.id);

  if (!category) {
    notFound();
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Breadcrumb */}
      <div className="mb-6">
        <Link 
          href="/categories" 
          className="inline-flex items-center text-rust-600 hover:text-rust-700 font-medium"
        >
          <ArrowLeft className="w-4 h-4 mr-1" />
          Back to Categories
        </Link>
      </div>

      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">{category.name}</h1>
        <p className="text-gray-600 mb-4">{category.description}</p>
        <p className="text-sm text-gray-500">
          {plugins.length} plugin{plugins.length !== 1 ? 's' : ''} in this category
        </p>
      </div>

      {/* Plugins */}
      {plugins.length === 0 ? (
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">No plugins yet</h3>
          <p className="text-gray-600">
            We're working on adding more plugins to this category. Check back soon!
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {plugins.map((plugin) => (
            <PluginCard key={plugin.id} plugin={plugin} />
          ))}
        </div>
      )}
    </div>
  );
}
