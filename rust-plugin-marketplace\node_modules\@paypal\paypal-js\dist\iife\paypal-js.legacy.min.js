/*!
 * paypal-js v8.2.0 (2025-01-23T17:26:53.747Z)
 * Copyright 2020-present, PayPal, Inc. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var paypalLoadScript=function(t){"use strict";function e(t,e){this.name="AggregateError",this.errors=t,this.message=e||""}e.prototype=Error.prototype;var n=setTimeout;function r(t){return Boolean(t&&void 0!==t.length)}function o(){}function i(t){if(!(this instanceof i))throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],l(t,this)}function a(t,e){for(;3===t._state;)t=t._value;0!==t._state?(t._handled=!0,i._immediateFn((function(){var n=1===t._state?e.onFulfilled:e.onRejected;if(null!==n){var r;try{r=n(t._value)}catch(t){return void u(e.promise,t)}c(e.promise,r)}else(1===t._state?c:u)(e.promise,t._value)}))):t._deferreds.push(e)}function c(t,e){try{if(e===t)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var n=e.then;if(e instanceof i)return t._state=3,t._value=e,void s(t);if("function"==typeof n)return void l((r=n,o=e,function(){r.apply(o,arguments)}),t)}t._state=1,t._value=e,s(t)}catch(e){u(t,e)}var r,o}function u(t,e){t._state=2,t._value=e,s(t)}function s(t){2===t._state&&0===t._deferreds.length&&i._immediateFn((function(){t._handled||i._unhandledRejectionFn(t._value)}));for(var e=0,n=t._deferreds.length;e<n;e++)a(t,t._deferreds[e]);t._deferreds=null}function f(t,e,n){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof e?e:null,this.promise=n}function l(t,e){var n=!1;try{t((function(t){n||(n=!0,c(e,t))}),(function(t){n||(n=!0,u(e,t))}))}catch(t){if(n)return;n=!0,u(e,t)}}i.prototype.catch=function(t){return this.then(null,t)},i.prototype.then=function(t,e){var n=new this.constructor(o);return a(this,new f(t,e,n)),n},i.prototype.finally=function(t){var e=this.constructor;return this.then((function(n){return e.resolve(t()).then((function(){return n}))}),(function(n){return e.resolve(t()).then((function(){return e.reject(n)}))}))},i.all=function(t){return new i((function(e,n){if(!r(t))return n(new TypeError("Promise.all accepts an array"));var o=Array.prototype.slice.call(t);if(0===o.length)return e([]);var i=o.length;function a(t,r){try{if(r&&("object"==typeof r||"function"==typeof r)){var c=r.then;if("function"==typeof c)return void c.call(r,(function(e){a(t,e)}),n)}o[t]=r,0==--i&&e(o)}catch(t){n(t)}}for(var c=0;c<o.length;c++)a(c,o[c])}))},i.any=function(t){var n=this;return new n((function(r,o){if(!t||void 0===t.length)return o(new TypeError("Promise.any accepts an array"));var i=Array.prototype.slice.call(t);if(0===i.length)return o();for(var a=[],c=0;c<i.length;c++)try{n.resolve(i[c]).then(r).catch((function(t){a.push(t),a.length===i.length&&o(new e(a,"All promises were rejected"))}))}catch(t){o(t)}}))},i.allSettled=function(t){return new this((function(e,n){if(!t||void 0===t.length)return n(new TypeError(typeof t+" "+t+" is not iterable(cannot read property Symbol(Symbol.iterator))"));var r=Array.prototype.slice.call(t);if(0===r.length)return e([]);var o=r.length;function i(t,n){if(n&&("object"==typeof n||"function"==typeof n)){var a=n.then;if("function"==typeof a)return void a.call(n,(function(e){i(t,e)}),(function(n){r[t]={status:"rejected",reason:n},0==--o&&e(r)}))}r[t]={status:"fulfilled",value:n},0==--o&&e(r)}for(var a=0;a<r.length;a++)i(a,r[a])}))},i.resolve=function(t){return t&&"object"==typeof t&&t.constructor===i?t:new i((function(e){e(t)}))},i.reject=function(t){return new i((function(e,n){n(t)}))},i.race=function(t){return new i((function(e,n){if(!r(t))return n(new TypeError("Promise.race accepts an array"));for(var o=0,a=t.length;o<a;o++)i.resolve(t[o]).then(e,n)}))},i._immediateFn="function"==typeof setImmediate&&function(t){setImmediate(t)}||function(t){n(t,0)},i._unhandledRejectionFn=function(t){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",t)};var d=Object.freeze({__proto__:null,default:i});function p(t){var e=t.sdkBaseUrl,n=t.environment,r=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(t);o<r.length;o++)e.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(t,r[o])&&(n[r[o]]=t[r[o]])}return n}(t,["sdkBaseUrl","environment"]),o=e||function(t){return"sandbox"===t?"https://www.sandbox.paypal.com/sdk/js":"https://www.paypal.com/sdk/js"}(n),i=r,a=Object.keys(i).filter((function(t){return void 0!==i[t]&&null!==i[t]&&""!==i[t]})).reduce((function(t,e){var n,r=i[e].toString();return n=function(t,e){return(e?"-":"")+t.toLowerCase()},"data"===(e=e.replace(/[A-Z]+(?![a-z])|[A-Z]/g,n)).substring(0,4)||"crossorigin"===e?t.attributes[e]=r:t.queryParams[e]=r,t}),{queryParams:{},attributes:{}}),c=a.queryParams,u=a.attributes;return c["merchant-id"]&&-1!==c["merchant-id"].indexOf(",")&&(u["data-merchant-id"]=c["merchant-id"],c["merchant-id"]="*"),{url:"".concat(o,"?").concat(h(c)),attributes:u}}function h(t){var e="";return Object.keys(t).forEach((function(n){0!==e.length&&(e+="&"),e+=n+"="+t[n]})),e}function y(t,e){void 0===e&&(e={});var n=document.createElement("script");return n.src=t,Object.keys(e).forEach((function(t){n.setAttribute(t,e[t]),"data-csp-nonce"===t&&n.setAttribute("nonce",e["data-csp-nonce"])})),n}function v(t,e){if(void 0===e&&(e=Promise),b(t,e),"undefined"==typeof document)return e.resolve(null);var n=p(t),r=n.url,o=n.attributes,i=o["data-namespace"]||"paypal",a=w(i);return o["data-js-sdk-library"]||(o["data-js-sdk-library"]="paypal-js"),function(t,e){var n=document.querySelector('script[src="'.concat(t,'"]'));if(null===n)return null;var r=y(t,e),o=n.cloneNode();if(delete o.dataset.uidAuto,Object.keys(o.dataset).length!==Object.keys(r.dataset).length)return null;var i=!0;return Object.keys(o.dataset).forEach((function(t){o.dataset[t]!==r.dataset[t]&&(i=!1)})),i?n:null}(r,o)&&a?e.resolve(a):m({url:r,attributes:o},e).then((function(){var t=w(i);if(t)return t;throw new Error("The window.".concat(i," global variable is not available."))}))}function m(t,e){void 0===e&&(e=Promise),b(t,e);var n=t.url,r=t.attributes;if("string"!=typeof n||0===n.length)throw new Error("Invalid url.");if(void 0!==r&&"object"!=typeof r)throw new Error("Expected attributes to be an object.");return new e((function(t,e){if("undefined"==typeof document)return t();!function(t){var e=t.url,n=t.attributes,r=t.onSuccess,o=t.onError,i=y(e,n);i.onerror=o,i.onload=r,document.head.insertBefore(i,document.head.firstElementChild)}({url:n,attributes:r,onSuccess:function(){return t()},onError:function(){var t=new Error('The script "'.concat(n,'" failed to load. Check the HTTP status code and response body in DevTools to learn more.'));return e(t)}})}))}function w(t){return window[t]}function b(t,e){if("object"!=typeof t||null===t)throw new Error("Expected an options object.");var n=t.environment;if(n&&"production"!==n&&"sandbox"!==n)throw new Error('The `environment` option must be either "production" or "sandbox".');if(void 0!==e&&"function"!=typeof e)throw new Error("Expected PromisePonyfill to be a function.")}"function"==typeof SuppressedError&&SuppressedError;return t.loadCustomScript=function(t){return m(t,d)},t.loadScript=function(t){return v(t,d)},t.version="8.2.0",t}({});window.paypalLoadCustomScript=paypalLoadScript.loadCustomScript,window.paypalLoadScript=paypalLoadScript.loadScript;
