/*!
 * react-paypal-js v8.8.3 (2025-04-11T19:50:46.506Z)
 * Copyright 2020-present, PayPal, Inc. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react");function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var r,n,o,a=t(e);exports.SCRIPT_LOADING_STATE=void 0,(r=exports.SCRIPT_LOADING_STATE||(exports.SCRIPT_LOADING_STATE={})).INITIAL="initial",r.PENDING="pending",r.REJECTED="rejected",r.RESOLVED="resolved",exports.DISPATCH_ACTION=void 0,(n=exports.DISPATCH_ACTION||(exports.DISPATCH_ACTION={})).LOADING_STATUS="setLoadingStatus",n.RESET_OPTIONS="resetOptions",n.SET_BRAINTREE_INSTANCE="braintreeInstance",exports.PAYPAL_HOSTED_FIELDS_TYPES=void 0,(o=exports.PAYPAL_HOSTED_FIELDS_TYPES||(exports.PAYPAL_HOSTED_FIELDS_TYPES={})).NUMBER="number",o.CVV="cvv",o.EXPIRATION_DATE="expirationDate",o.EXPIRATION_MONTH="expirationMonth",o.EXPIRATION_YEAR="expirationYear",o.POSTAL_CODE="postalCode";var i=function(){return i=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},i.apply(this,arguments)};function c(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r}function s(e,t,r){if(r||2===arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}"function"==typeof SuppressedError&&SuppressedError;var l="data-react-paypal-script-id",u="dataClientToken",d="dataJsSdkLibrary",f="react-paypal-js",p="dataNamespace",v="dataSdkIntegrationSource",P="dataUserIdToken",m="Failed to load the PayPal JS SDK script.",E="3.117.0",h="https://js.braintreegateway.com/web/".concat(E,"/js/client.min.js"),y="https://js.braintreegateway.com/web/".concat(E,"/js/paypal-checkout.min.js"),S="paypal",T="usePayPalScriptReducer must be used within a PayPalScriptProvider";function A(e){return void 0===e&&(e=S),window[e]}function I(e){var t=e.reactComponentName,r=e.sdkComponentKey,n=e.sdkRequestedComponents,o=void 0===n?"":n,a=e.sdkDataNamespace,i=void 0===a?S:a,c=r.charAt(0).toUpperCase().concat(r.substring(1)),s="Unable to render <".concat(t," /> because window.").concat(i,".").concat(c," is undefined."),l="string"==typeof o?o:o.join(",");if(!l.includes(r)){var u=[l,r].filter(Boolean).join();s+="\nTo fix the issue, add '".concat(r,"' to the list of components passed to the parent PayPalScriptProvider:")+"\n`<PayPalScriptProvider options={{ components: '".concat(u,"'}}>`.")}return s}function N(e){var t=e,r=l;t[r];var n=c(t,[r+""]);return"react-paypal-js-".concat(function(e){for(var t="",r=0;r<e.length;r++){var n=e[r].charCodeAt(0)*r;e[r+1]&&(n+=e[r+1].charCodeAt(0)*(r-1)),t+=String.fromCharCode(97+Math.abs(n)%26)}return t}(JSON.stringify(n)))}function C(e){var t=self.document.querySelector("script[".concat(l,'="').concat(e,'"]'));(null==t?void 0:t.parentNode)&&t.parentNode.removeChild(t)}function b(e,t){var r,n;switch(t.type){case exports.DISPATCH_ACTION.LOADING_STATUS:return"object"==typeof t.value?i(i({},e),{loadingStatus:t.value.state,loadingStatusErrorMessage:t.value.message}):i(i({},e),{loadingStatus:t.value});case exports.DISPATCH_ACTION.RESET_OPTIONS:return C(e.options[l]),i(i({},e),{loadingStatus:exports.SCRIPT_LOADING_STATE.PENDING,options:i(i((r={},r[v]=f,r),t.value),(n={},n[l]="".concat(N(t.value)),n))});case exports.DISPATCH_ACTION.SET_BRAINTREE_INSTANCE:return i(i({},e),{braintreePayPalCheckoutInstance:t.value});default:return e}}var O=e.createContext(null);function g(e){if("function"==typeof(null==e?void 0:e.dispatch)&&0!==e.dispatch.length)return e;throw new Error(T)}function w(){var t=g(e.useContext(O));return[i(i({},t),{isInitial:t.loadingStatus===exports.SCRIPT_LOADING_STATE.INITIAL,isPending:t.loadingStatus===exports.SCRIPT_LOADING_STATE.PENDING,isResolved:t.loadingStatus===exports.SCRIPT_LOADING_STATE.RESOLVED,isRejected:t.loadingStatus===exports.SCRIPT_LOADING_STATE.REJECTED}),t.dispatch]}function x(){var t=function(e){var t,r;if(!(null===(t=null==e?void 0:e.options)||void 0===t?void 0:t[u])&&!(null===(r=null==e?void 0:e.options)||void 0===r?void 0:r[P]))throw new Error("Invalid authorization data. Use dataClientToken or dataUserIdToken to authorize.");return e}(g(e.useContext(O)));return[t,t.dispatch]}var R=e.createContext({});var _=function(t){var r,n,o,l=t.className,u=void 0===l?"":l,d=t.disabled,f=void 0!==d&&d,v=t.children,P=t.forceReRender,m=void 0===P?[]:P,E=c(t,["className","disabled","children","forceReRender"]),h=f?{opacity:.38}:{},y="".concat(u," ").concat(f?"paypal-buttons-disabled":"").trim(),S=e.useRef(null),T=e.useRef(null),N=(n=E,o=e.useRef(new Proxy({},{get:function(e,t,r){return"function"==typeof e[t]?function(){for(var r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];return e[t].apply(e,r)}:Reflect.get(e,t,r)}})),o.current=Object.assign(o.current,n),o.current),C=w()[0],b=C.isResolved,O=C.options,g=e.useState(null),x=g[0],R=g[1],F=e.useState(!0),D=F[0],L=F[1],k=e.useState(null)[1];function j(){null!==T.current&&T.current.close().catch((function(){}))}return(null===(r=T.current)||void 0===r?void 0:r.updateProps)&&T.current.updateProps({message:E.message}),e.useEffect((function(){if(!1===b)return j;var e=A(O.dataNamespace);if(void 0===e||void 0===e.Buttons)return k((function(){throw new Error(I({reactComponentName:_.displayName,sdkComponentKey:"buttons",sdkRequestedComponents:O.components,sdkDataNamespace:O[p]}))})),j;try{T.current=e.Buttons(i(i({},N),{onInit:function(e,t){R(t),"function"==typeof E.onInit&&E.onInit(e,t)}}))}catch(e){return k((function(){throw new Error("Failed to render <PayPalButtons /> component. Failed to initialize:  ".concat(e))}))}return!1===T.current.isEligible()?(L(!1),j):S.current?(T.current.render(S.current).catch((function(e){null!==S.current&&0!==S.current.children.length&&k((function(){throw new Error("Failed to render <PayPalButtons /> component. ".concat(e))}))})),j):j}),s(s([b],m,!0),[E.fundingSource],!1)),e.useEffect((function(){null!==x&&(!0===f?x.disable().catch((function(){})):x.enable().catch((function(){})))}),[f,x]),a.default.createElement(a.default.Fragment,null,D?a.default.createElement("div",{ref:S,style:h,className:y}):v)};function F(e){var t=e.sdkBaseUrl,r=e.environment,n=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r}(e,["sdkBaseUrl","environment"]),o=t||function(e){return"sandbox"===e?"https://www.sandbox.paypal.com/sdk/js":"https://www.paypal.com/sdk/js"}(r),a=n,i=Object.keys(a).filter((function(e){return void 0!==a[e]&&null!==a[e]&&""!==a[e]})).reduce((function(e,t){var r,n=a[t].toString();return r=function(e,t){return(t?"-":"")+e.toLowerCase()},"data"===(t=t.replace(/[A-Z]+(?![a-z])|[A-Z]/g,r)).substring(0,4)||"crossorigin"===t?e.attributes[t]=n:e.queryParams[t]=n,e}),{queryParams:{},attributes:{}}),c=i.queryParams,s=i.attributes;return c["merchant-id"]&&-1!==c["merchant-id"].indexOf(",")&&(s["data-merchant-id"]=c["merchant-id"],c["merchant-id"]="*"),{url:"".concat(o,"?").concat(D(c)),attributes:s}}function D(e){var t="";return Object.keys(e).forEach((function(r){0!==t.length&&(t+="&"),t+=r+"="+e[r]})),t}function L(e,t){void 0===t&&(t={});var r=document.createElement("script");return r.src=e,Object.keys(t).forEach((function(e){r.setAttribute(e,t[e]),"data-csp-nonce"===e&&r.setAttribute("nonce",t["data-csp-nonce"])})),r}function k(e,t){if(void 0===t&&(t=Promise),G(e,t),"undefined"==typeof document)return t.resolve(null);var r=F(e),n=r.url,o=r.attributes,a=o["data-namespace"]||"paypal",i=H(a);return o["data-js-sdk-library"]||(o["data-js-sdk-library"]="paypal-js"),function(e,t){var r=document.querySelector('script[src="'.concat(e,'"]'));if(null===r)return null;var n=L(e,t),o=r.cloneNode();if(delete o.dataset.uidAuto,Object.keys(o.dataset).length!==Object.keys(n.dataset).length)return null;var a=!0;return Object.keys(o.dataset).forEach((function(e){o.dataset[e]!==n.dataset[e]&&(a=!1)})),a?r:null}(n,o)&&i?t.resolve(i):j({url:n,attributes:o},t).then((function(){var e=H(a);if(e)return e;throw new Error("The window.".concat(a," global variable is not available."))}))}function j(e,t){void 0===t&&(t=Promise),G(e,t);var r=e.url,n=e.attributes;if("string"!=typeof r||0===r.length)throw new Error("Invalid url.");if(void 0!==n&&"object"!=typeof n)throw new Error("Expected attributes to be an object.");return new t((function(e,t){if("undefined"==typeof document)return e();!function(e){var t=e.url,r=e.attributes,n=e.onSuccess,o=e.onError,a=L(t,r);a.onerror=o,a.onload=n,document.head.insertBefore(a,document.head.firstElementChild)}({url:r,attributes:n,onSuccess:function(){return e()},onError:function(){var e=new Error('The script "'.concat(r,'" failed to load. Check the HTTP status code and response body in DevTools to learn more.'));return t(e)}})}))}function H(e){return window[e]}function G(e,t){if("object"!=typeof e||null===e)throw new Error("Expected an options object.");var r=e.environment;if(r&&"production"!==r&&"sandbox"!==r)throw new Error('The `environment` option must be either "production" or "sandbox".');if(void 0!==t&&"function"!=typeof t)throw new Error("Expected PromisePonyfill to be a function.")}_.displayName="PayPalButtons","function"==typeof SuppressedError&&SuppressedError;var Y=function(e){return e&&function(e){var t,r;if("function"!=typeof(null===(t=null==e?void 0:e.client)||void 0===t?void 0:t.create)&&"function"!=typeof(null===(r=null==e?void 0:e.paypalCheckout)||void 0===r?void 0:r.create))throw new Error("The braintreeNamespace property is not a valid BraintreeNamespace type.");return!0}(e)?Promise.resolve(e):Promise.all([j({url:h}),j({url:y})]).then((function(){return void 0===e&&(e="braintree"),window[e];var e}))},B=function(t){var r=t.className,n=void 0===r?"":r,o=t.children,s=c(t,["className","children"]),l=w()[0],u=l.isResolved,d=l.options,f=e.useRef(null),v=e.useState(!0),P=v[0],m=v[1],E=e.useState(null)[1];return e.useEffect((function(){if(!1!==u){var e=A(d[p]);if(void 0===e||void 0===e.Marks)return E((function(){throw new Error(I({reactComponentName:B.displayName,sdkComponentKey:"marks",sdkRequestedComponents:d.components,sdkDataNamespace:d[p]}))}));!function(e){var t=f.current;if(!t||!e.isEligible())return m(!1);t.firstChild&&t.removeChild(t.firstChild),e.render(t).catch((function(e){null!==t&&0!==t.children.length&&E((function(){throw new Error("Failed to render <PayPalMarks /> component. ".concat(e))}))}))}(e.Marks(i({},s)))}}),[u,s.fundingSource]),a.default.createElement(a.default.Fragment,null,P?a.default.createElement("div",{ref:f,className:n}):o)};B.displayName="PayPalMarks";var M=function(t){var r=t.className,n=void 0===r?"":r,o=t.forceReRender,l=void 0===o?[]:o,u=c(t,["className","forceReRender"]),d=w()[0],f=d.isResolved,v=d.options,P=e.useRef(null),m=e.useRef(null),E=e.useState(null)[1];return e.useEffect((function(){if(!1!==f){var e=A(v[p]);if(void 0===e||void 0===e.Messages)return E((function(){throw new Error(I({reactComponentName:M.displayName,sdkComponentKey:"messages",sdkRequestedComponents:v.components,sdkDataNamespace:v[p]}))}));m.current=e.Messages(i({},u)),m.current.render(P.current).catch((function(e){null!==P.current&&0!==P.current.children.length&&E((function(){throw new Error("Failed to render <PayPalMessages /> component. ".concat(e))}))}))}}),s([f],l,!0)),a.default.createElement("div",{ref:P,className:n})};M.displayName="PayPalMessages";var U=function(e){if(!e.includes(exports.PAYPAL_HOSTED_FIELDS_TYPES.NUMBER)||!e.includes(exports.PAYPAL_HOSTED_FIELDS_TYPES.CVV)||function(e){return!e.includes(exports.PAYPAL_HOSTED_FIELDS_TYPES.EXPIRATION_DATE)&&!e.includes(exports.PAYPAL_HOSTED_FIELDS_TYPES.EXPIRATION_MONTH)&&!e.includes(exports.PAYPAL_HOSTED_FIELDS_TYPES.EXPIRATION_YEAR)}(e))throw new Error("To use HostedFields you must use it with at least 3 children with types: [number, cvv, expirationDate] includes")},V=function(e){U(e),function(e){if(e.length!==new Set(e).size)throw new Error("Cannot use duplicate HostedFields as children")}(e)};function q(){}var X=e.createContext({cardFieldsForm:null,fields:{},registerField:q,unregisterField:q}),z=function(){return e.useContext(X)},K=function(e){var t=e.children;return a.default.createElement("div",{style:{width:"100%"}},t)},J=function(t){var r=t.className,n=t.fieldName,o=c(t,["className","fieldName"]),i=z(),s=i.cardFieldsForm,l=i.registerField,u=i.unregisterField,d=e.useRef(null),f=e.useState(null)[1];function p(){u(n)}return e.useEffect((function(){if(!s)return f((function(){throw new Error("Individual CardFields must be rendered inside the PayPalCardFieldsProvider")})),p;if(!d.current)return p;var e=l(n,o,s);return null==e||e.render(d.current).catch((function(e){(function(e){var t;return!!(null===(t=e.current)||void 0===t?void 0:t.children.length)})(d)&&f((function(){throw new Error("Failed to render <PayPal".concat(n," /> component. ").concat(e))}))})),p}),[]),a.default.createElement("div",{ref:d,className:r})},Z=function(e){var t=e.children;return a.default.createElement("div",{style:{display:"flex",width:"100%"}},t)},W={PAYPAL:"paypal",VENMO:"venmo",APPLEPAY:"applepay",ITAU:"itau",CREDIT:"credit",PAYLATER:"paylater",CARD:"card",IDEAL:"ideal",SEPA:"sepa",BANCONTACT:"bancontact",GIROPAY:"giropay",SOFORT:"sofort",EPS:"eps",MYBANK:"mybank",P24:"p24",PAYU:"payu",BLIK:"blik",TRUSTLY:"trustly",OXXO:"oxxo",BOLETO:"boleto",BOLETOBANCARIO:"boletobancario",WECHATPAY:"wechatpay",MERCADOPAGO:"mercadopago",MULTIBANCO:"multibanco",SATISPAY:"satispay",PAIDY:"paidy",ZIMPLER:"zimpler",MAXIMA:"maxima"},Q=W;exports.BraintreePayPalButtons=function(t){var r=t.className,n=void 0===r?"":r,o=t.disabled,s=void 0!==o&&o,l=t.children,d=t.forceReRender,f=void 0===d?[]:d,p=t.braintreeNamespace,v=t.merchantAccountId,E=c(t,["className","disabled","children","forceReRender","braintreeNamespace","merchantAccountId"]),h=e.useState(null)[1],y=x(),S=y[0],T=y[1];return e.useEffect((function(){Y(p).then((function(e){var t=S.options[P],r=S.options[u];return e.client.create({authorization:t||r}).then((function(t){var r=v?{merchantAccountId:v}:{};return e.paypalCheckout.create(i(i({},r),{client:t}))})).then((function(e){T({type:exports.DISPATCH_ACTION.SET_BRAINTREE_INSTANCE,value:e})}))})).catch((function(e){h((function(){throw new Error("".concat(m," ").concat(e))}))}))}),[S.options]),a.default.createElement(a.default.Fragment,null,S.braintreePayPalCheckoutInstance&&a.default.createElement(_,i({className:n,disabled:s,forceReRender:f},function(e,t){var r=e.createOrder,n=e.createBillingAgreement,o=e.onApprove;return"function"==typeof r&&(e.createOrder=function(e,n){return r(e,i(i({},n),{braintree:t}))}),"function"==typeof n&&(e.createBillingAgreement=function(e,r){return n(e,i(i({},r),{braintree:t}))}),"function"==typeof o&&(e.onApprove=function(e,r){return o(e,i(i({},r),{braintree:t}))}),i({},e)}(E,S.braintreePayPalCheckoutInstance)),l))},exports.FUNDING=Q,exports.PayPalButtons=_,exports.PayPalCVVField=function(e){return a.default.createElement(J,i({fieldName:"CVVField"},e))},exports.PayPalCardFieldsContext=X,exports.PayPalCardFieldsForm=function(e){var t=e.className;return a.default.createElement("div",{className:t},a.default.createElement(J,{fieldName:"NameField"}),a.default.createElement(J,{fieldName:"NumberField"}),a.default.createElement(Z,null,a.default.createElement(K,null,a.default.createElement(J,{fieldName:"ExpiryField"})),a.default.createElement(K,null,a.default.createElement(J,{fieldName:"CVVField"}))))},exports.PayPalCardFieldsProvider=function(t){var r=t.children,n=c(t,["children"]),o=w()[0],s=o.isResolved,l=o.options,u=function(){var t=e.useState(null)[1],r=e.useRef({});return{fields:r.current,registerField:function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var o=e[0],a=e[1],i=e[2];return r.current[o]&&t((function(){throw new Error("Cannot use duplicate CardFields as children")})),r.current[o]=null==i?void 0:i[o](a),r.current[o]},unregisterField:function(e){var t=r.current[e];t&&(t.close().catch(q),delete r.current[e])}}}(),d=u.fields,f=u.registerField,v=u.unregisterField,P=e.useState(null),m=P[0],E=P[1],h=e.useRef(null),y=e.useState(!1),T=y[0],I=y[1],N=e.useState(null)[1];return e.useEffect((function(){var e,t,r;if(s){try{h.current=null!==(r=null===(t=(e=A(l[p])).CardFields)||void 0===t?void 0:t.call(e,i({},n)))&&void 0!==r?r:null}catch(e){return void N((function(){throw new Error("Failed to render <PayPalCardFieldsProvider /> component. Failed to initialize:  ".concat(e))}))}if(h.current)return I(h.current.isEligible()),E(h.current),function(){E(null),h.current=null};N((function(){var e;throw new Error(function(e){var t=e.components,r=void 0===t?"":t,n=e[p],o=void 0===n?S:n,a=r?"".concat(r,",card-fields"):"card-fields",i="Unable to render <PayPalCardFieldsProvider /> because window.".concat(o,".CardFields is undefined.");return r.includes("card-fields")||(i+="\nTo fix the issue, add 'card-fields' to the list of components passed to the parent PayPalScriptProvider: <PayPalScriptProvider options={{ components: '".concat(a,"'}}>")),i}(((e={components:l.components})[p]=l[p],e)))}))}}),[s]),T?a.default.createElement(K,null,a.default.createElement(X.Provider,{value:{cardFieldsForm:m,fields:d,registerField:f,unregisterField:v}},r)):a.default.createElement("div",null)},exports.PayPalExpiryField=function(e){return a.default.createElement(J,i({fieldName:"ExpiryField"},e))},exports.PayPalHostedField=function(t){var r=t.hostedFieldType,n=t.options,o=c(t,["hostedFieldType","options"]),s=e.useContext(R);return e.useEffect((function(){var e;if(!(null==s?void 0:s.registerHostedField))throw new Error("The HostedField cannot be register in the PayPalHostedFieldsProvider parent component");s.registerHostedField(((e={})[r]={selector:n.selector,placeholder:n.placeholder,type:n.type,formatInput:n.formatInput,maskInput:n.maskInput,select:n.select,maxlength:n.maxlength,minlength:n.minlength,prefill:n.prefill,rejectUnsupportedCards:n.rejectUnsupportedCards},e))}),[]),a.default.createElement("div",i({},o))},exports.PayPalHostedFieldsProvider=function(t){var r=t.styles,n=t.createOrder,o=t.notEligibleError,c=t.children,s=t.installments,l=x()[0],u=l.options,d=l.loadingStatus,f=e.useState(!0),v=f[0],P=f[1],m=e.useState(),E=m[0],h=m[1],y=e.useState(null)[1],T=e.useRef(null),I=e.useRef(),N=function(t){void 0===t&&(t={});var r=e.useRef(t);return[r,function(e){r.current=i(i({},r.current),e)}]}(),C=N[0],b=N[1];return e.useEffect((function(){var e;if(V(Object.keys(C.current)),d===exports.SCRIPT_LOADING_STATE.RESOLVED){if(I.current=A(u[p]).HostedFields,!I.current)throw new Error(function(e){var t=e.components,r=void 0===t?"":t,n=e[p],o=void 0===n?S:n,a=r?"".concat(r,",hosted-fields"):"hosted-fields",i="Unable to render <PayPalHostedFieldsProvider /> because window.".concat(o,".HostedFields is undefined.");return r.includes("hosted-fields")||(i+="\nTo fix the issue, add 'hosted-fields' to the list of components passed to the parent PayPalScriptProvider: <PayPalScriptProvider options={{ components: '".concat(a,"'}}>")),i}(((e={components:u.components})[p]=u[p],e)));if(!I.current.isEligible())return P(!1);E&&E.teardown(),I.current.render({createOrder:n,fields:C.current,installments:s,styles:r}).then((function(e){T.current&&h(e)})).catch((function(e){y((function(){throw new Error("Failed to render <PayPalHostedFieldsProvider /> component. ".concat(e))}))}))}}),[d,r]),a.default.createElement("div",{ref:T},v?a.default.createElement(R.Provider,{value:{cardFields:E,registerHostedField:b}},c):o)},exports.PayPalMarks=B,exports.PayPalMessages=M,exports.PayPalNameField=function(e){return a.default.createElement(J,i({fieldName:"NameField"},e))},exports.PayPalNumberField=function(e){return a.default.createElement(J,i({fieldName:"NumberField"},e))},exports.PayPalScriptProvider=function(t){var r,n=t.options,o=void 0===n?{clientId:"test"}:n,c=t.children,s=t.deferLoading,u=void 0!==s&&s,p=e.useReducer(b,{options:i(i({},o),(r={},r[d]=f,r[v]=f,r[l]="".concat(N(o)),r)),loadingStatus:u?exports.SCRIPT_LOADING_STATE.INITIAL:exports.SCRIPT_LOADING_STATE.PENDING}),P=p[0],E=p[1];return e.useEffect((function(){if(!1===u&&P.loadingStatus===exports.SCRIPT_LOADING_STATE.INITIAL)return E({type:exports.DISPATCH_ACTION.LOADING_STATUS,value:exports.SCRIPT_LOADING_STATE.PENDING});if(P.loadingStatus===exports.SCRIPT_LOADING_STATE.PENDING){var e=!0;return k(P.options).then((function(){e&&E({type:exports.DISPATCH_ACTION.LOADING_STATUS,value:exports.SCRIPT_LOADING_STATE.RESOLVED})})).catch((function(t){console.error("".concat(m," ").concat(t)),e&&E({type:exports.DISPATCH_ACTION.LOADING_STATUS,value:{state:exports.SCRIPT_LOADING_STATE.REJECTED,message:String(t)}})})),function(){e=!1}}}),[P.options,u,P.loadingStatus]),a.default.createElement(O.Provider,{value:i(i({},P),{dispatch:E})},c)},exports.ScriptContext=O,exports.destroySDKScript=C,exports.getScriptID=N,exports.scriptReducer=b,exports.usePayPalCardFields=z,exports.usePayPalHostedFields=function(){return e.useContext(R)},exports.usePayPalScriptReducer=w,exports.useScriptProviderContext=x;
